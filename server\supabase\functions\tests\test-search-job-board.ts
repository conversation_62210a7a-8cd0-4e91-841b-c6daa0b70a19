import { setEvents, startDeno, setTheirStackClient } from "../search-job-board/index.ts";
import { load } from 'jsr:@std/dotenv';
import { createCustomer, post, TestEvents, upsertOnboarding, upsertPlan } from './test-common.ts';
import { assertEquals, assertNotEquals, assertExists } from 'jsr:@std/assert';
import { Customer, getCustomerSearchCriteria, PRODUCT_CODE, supabaseClient, CustomerSearchCriteria, JOB_BOARDS, Job } from '../shared/common.ts';
import { v4 as uuid } from 'npm:uuid';

// Extended test events class to capture job search events
export class TestJobSearchEvents extends TestEvents {
  publishedJobsFound: any[] = [];

  async publishJobsFound(payload: any[], topic: string): Promise<void> {
    payload.forEach((event) => (
      this.publishedJobsFound.push(event)
    ));
    return Promise.resolve();
  }
}

// Mock job board client for testing
export class MockTheirStackClient {
  private mockJobs: Job[] = [
    {
      id: "theirstack_job-1",
      title: "Senior Software Engineer",
      employer: "TechCorp Inc",
      location: "San Francisco, CA",
      description: "We are looking for a senior software engineer with 5+ years of experience in TypeScript and React.",
      url: "https://theirstack.com/jobs/job-1",
      job_type: "FULLTIME",
      job_status: "OPEN",
      pay_amount: 140000,
      pay_currency: "USD",
      src_id: "job-1",
      account_required: false,
      customer_apply: true,
      created_at: new Date().toISOString(),
      modified_time: new Date().toISOString()
    },
    {
      id: "theirstack_job-2",
      title: "Frontend Developer",
      employer: "StartupXYZ",
      location: "Remote",
      description: "Join our team as a frontend developer working with modern web technologies.",
      url: "https://theirstack.com/jobs/job-2",
      job_type: "FULLTIME",
      job_status: "OPEN",
      pay_amount: 100000,
      pay_currency: "USD",
      src_id: "job-2",
      account_required: false,
      customer_apply: true,
      created_at: new Date().toISOString(),
      modified_time: new Date().toISOString()
    }
  ];

  async searchJobs(
    jobTitles: string[],
    locations: string[],
    _options?: { limit?: number; offset?: number }
  ): Promise<{ jobs: Job[], totalCount: number, hasMore: boolean }> {
    // Simple mock implementation - filter jobs based on title and location
    const filteredJobs = this.mockJobs.filter(job => {
      const titleMatch = jobTitles.some(title =>
        job.title.toLowerCase().includes(title.toLowerCase())
      );
      const locationMatch = locations.some(location =>
        job.location.toLowerCase().includes(location.toLowerCase()) ||
        job.location.toLowerCase() === "remote"
      );
      return titleMatch && locationMatch;
    });

    return {
      jobs: filteredJobs,
      totalCount: filteredJobs.length,
      hasMore: false
    };
  }

  // Method to add mock jobs for testing
  addMockJob(job: Job): void {
    this.mockJobs.push(job);
  }

  // Method to clear mock jobs
  clearMockJobs(): void {
    this.mockJobs = [];
  }

  // Mock methods to match TheirStackClient interface
  async testConnection(): Promise<boolean> {
    return true;
  }
}



Deno.test("search-job-board: should process search criteria and find jobs", async () => {
  await load({ export: true });
  startDeno();

  const supabase = supabaseClient();

  // Setup mock TheirStack client
  const mockClient = new MockTheirStackClient();
  setTheirStackClient(mockClient);

  // Setup test customer with search criteria
  const customer: Customer = await createCustomer("<EMAIL>", supabase);
  await upsertPlan(customer.id, PRODUCT_CODE.APPS_20, supabase);

  const jobTitles = ["Software Engineer", "Developer"];
  const locations = ["San Francisco", "Remote"];
  await upsertOnboarding(customer.id, jobTitles, locations, supabase);

  // Create a job board entry
  const { data: jobBoard, error: boardError } = await supabase
    .from("job_boards")
    .upsert({
      id: "theirstack-board-id",
      key: "theirstack",
      url: "https://theirstack.com"
    }, { onConflict: "id" })
    .select()
    .single();

  if (boardError) {
    throw new Error(`Failed to create job board: ${boardError.message}`);
  }

  // Create search criteria
  const searchCriteria: CustomerSearchCriteria = {
    id: uuid(),
    search_date: "2025-01-20",
    job_board_id: jobBoard.id,
    customer_id: customer.id,
    job_titles: jobTitles,
    locations: locations,
    search_status: "NEW"
  };

  // Save search criteria to database
  const { error } = await supabase
    .from("customer_search_criteria")
    .insert(searchCriteria);

  if (error) {
    throw new Error(`Failed to insert search criteria: ${error.message}`);
  }

  // Setup mock events
  const events = new TestJobSearchEvents();
  setEvents(events);

  // Create request payload
  const payload = [{ customerSearchCriteriaId: searchCriteria.id }];

  // Make request to search-job-board function
  await post(JSON.stringify(payload));

  // Verify that jobs were found and saved
  const { data: savedJobs, error: jobsError } = await supabase
    .from("jobs")
    .select("*")
    .in("src_id", ["job-1", "job-2"]);

  if (jobsError) {
    throw new Error(`Failed to fetch saved jobs: ${jobsError.message}`);
  }

  // Assertions
  assertNotEquals(savedJobs.length, 0, "No jobs were saved to database");
  assertExists(savedJobs.find(job => job.title.includes("Engineer")), "Expected job not found");

  // Verify search criteria status was updated
  const updatedCriteria = await getCustomerSearchCriteria(searchCriteria.id, supabase);
  assertEquals(updatedCriteria.search_status, "COMPLETE", "Search criteria status not updated");

  // Verify events were published
  assertNotEquals(events.publishedJobsFound.length, 0, "No job found events were published");

  console.log(`Successfully found and saved ${savedJobs.length} jobs`);
});

Deno.test("search-job-board: should handle empty search results", async () => {
  await load({ export: true });
  startDeno();
  
  const supabase = supabaseClient();
  
  // Setup test customer with search criteria that won't match any jobs
  const customer: Customer = await createCustomer("<EMAIL>", supabase);
  await upsertPlan(customer.id, PRODUCT_CODE.APPS_20, supabase);
  
  const jobTitles = ["Unicorn Trainer"]; // Job title that won't match
  const locations = ["Mars"];           // Location that won't match
  await upsertOnboarding(customer.id, jobTitles, locations, supabase);

  // Create search criteria
  const searchCriteria: CustomerSearchCriteria = {
    id: uuid(),
    search_date: "2025-01-20",
    job_board_id: "theirstack-board-id",
    customer_id: customer.id,
    job_titles: jobTitles,
    locations: locations,
    search_status: "NEW"
  };

  // Save search criteria to database
  const { error } = await supabase
    .from("customer_search_criteria")
    .insert(searchCriteria);
  
  if (error) {
    throw new Error(`Failed to insert search criteria: ${error.message}`);
  }

  // Setup mock events
  const events = new TestJobSearchEvents();
  setEvents(events);

  // Create request payload
  const payload = [{ customerSearchCriteriaId: searchCriteria.id }];
  
  // Make request to search-job-board function
  await post(JSON.stringify(payload));

  // Verify search criteria status was still updated even with no results
  const updatedCriteria = await getCustomerSearchCriteria(searchCriteria.id, supabase);
  assertEquals(updatedCriteria.search_status, "COMPLETE", "Search criteria status not updated");

  console.log("Successfully handled empty search results");
});

Deno.test("search-job-board: should handle invalid search criteria ID", async () => {
  await load({ export: true });
  startDeno();

  // Setup mock events
  const events = new TestJobSearchEvents();
  setEvents(events);

  // Create request payload with invalid ID
  const payload = [{ customerSearchCriteriaId: "invalid-id-12345" }];
  
  // Make request to search-job-board function
  // This should not throw an error but should handle gracefully
  await post(JSON.stringify(payload));

  console.log("Successfully handled invalid search criteria ID");
});
