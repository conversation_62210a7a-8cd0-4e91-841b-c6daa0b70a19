"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateDomainRequest = exports.CreateDomainRequest = void 0;
var CreateDomainRequest = /** @class */ (function () {
    function CreateDomainRequest(Name, ReturnPathDomain) {
        this.Name = Name;
        this.ReturnPathDomain = ReturnPathDomain;
    }
    return CreateDomainRequest;
}());
exports.CreateDomainRequest = CreateDomainRequest;
var UpdateDomainRequest = /** @class */ (function () {
    function UpdateDomainRequest(ReturnPathDomain) {
        this.ReturnPathDomain = ReturnPathDomain;
    }
    return UpdateDomainRequest;
}());
exports.UpdateDomainRequest = UpdateDomainRequest;
//# sourceMappingURL=Domain.js.map