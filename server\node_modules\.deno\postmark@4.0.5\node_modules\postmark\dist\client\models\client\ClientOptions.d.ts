export declare namespace ClientOptions {
    class Configuration {
        useHttps?: boolean;
        requestHost?: string;
        timeout?: number;
        constructor(useHttps?: boolean, requestHost?: string, timeout?: number);
    }
    enum HttpMethod {
        GET = "GET",
        POST = "POST",
        DELETE = "DELETE",
        PUT = "PUT",
        OPTIONS = "OPTIONS",
        HEAD = "HEAD",
        PATCH = "PATCH"
    }
    enum AuthHeaderNames {
        SERVER_TOKEN = "X-Postmark-Server-Token",
        ACCOUNT_TOKEN = "X-Postmark-Account-Token"
    }
}
