import type { ContextFn, DateArg } from "../types.js";
export declare function normalizeDates(
  context: ContextFn<Date> | undefined,
  ...dates: [DateArg<Date>, DateArg<Date>, DateArg<Date>]
): [Date, Date, Date];
export declare function normalizeDates(
  context: ContextFn<Date> | undefined,
  ...dates: [DateArg<Date>, DateArg<Date>]
): [Date, Date];
export declare function normalizeDates(
  context: ContextFn<Date> | undefined,
  ...dates: Array<DateArg<Date> & {}>
): Date[];
