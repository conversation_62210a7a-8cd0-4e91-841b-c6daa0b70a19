import { Database } from "../lib/database.types.ts"
import { createClient, PostgrestError, SupabaseClient } from 'jsr:@supabase/supabase-js@2'
import * as postmark from 'npm:postmark';
import { Client as QstashClient, Receiver } from 'npm:@upstash/qstash';
import vento from "jsr:@vento/vento";
import { load } from 'jsr:@std/dotenv';
import { format, parse } from 'npm:date-fns';


/* 
 * General purpose helpers
 */

export const DATE_FORMAT = "yyyy-MM-dd";
export function dateStr(date: Date): string {
  return format(date, DATE_FORMAT);
}
export function toDate(date: string): Date {
  return parse(date, DATE_FORMAT, new Date());
}

export function getEnumKey<T>(enumType: T, key: keyof T): T[keyof T] {
    return enumType[key];
}

export function supabaseClient(): SupabaseClient<Database> {
    console.info(`initializing Supabase client...`)
    const database = createClient<Database>(
        Deno.env.get("SUPABASE_URL")!,
        Deno.env.get("OUR_FUNCTION_KEY")!,
        {
            auth: {
                // Due to https://github.com/supabase/auth-js/issues/856
                // Without this, Deno blows up b/c it detects an Interval
                // was started and not stopped.
                autoRefreshToken: false,
            }
        }
    )
    return database;
}

export enum SupabaseErrorCodes {
    USER_EXISTS = 'user_already_exists'
}

export function isTransientError(error: PostgrestError): boolean {
    const transientErrorCodes = [
      'ECONNRESET',
      'ETIMEDOUT',
      'EAI_AGAIN', // DNS lookup timeout
      'ECONNREFUSED',
      'EHOSTUNREACH'
    ];
  
    // Check if error has a code that suggests a temporary network issue
    if (error?.code && transientErrorCodes.includes(error.code)) {
      return true;
    }
  
    // Check HTTP status codes
    if (error?.code && [429, 500, 502, 503, 504].includes(parseInt(error.code))) {
      return true;
    }
  
    return false;
  }

  export function handleError(error: PostgrestError|undefined|null): boolean {

    if (error && isTransientError(error)) {
        console.error("Transient error occurred, retry", error);
        throw error;
    } else if (error) {
        console.error("Non-transient error occurred, allowing to proceed to allow for idempotent process", error);
        return true;
    } else {
        return true;
    }

  }

export interface Email {
  to: string;
  subject: string;
  body: string;
}

export interface Postmark {
  sendEmail(email: Email): Promise<void>;
}

export class LivePostmark implements Postmark {
  private client: postmark.ServerClient;
  
  constructor() {
    load();
    this.client = new postmark.ServerClient(Deno.env.get("POSTMARK_KEY")!);
  }

  async sendEmail(email: Email): Promise<void> {
    console.log(`sending email via Postmark... to ${email.to}`)
    await this.client.sendEmail(
      {
          From: "Applysquad Notifications <<EMAIL>>",
          To: email.to,
          Subject: email.subject,
          HtmlBody: email.body
      }
    ).then(response => {
      console.log(`Sending message via Postmark to ${response.To}`);
      console.log(`Postmark response: ${response.Message}`);
      });
    return;
  }
  
}

export async function template(templateContent: string, data: Record<string, unknown>): Promise<string> {
  const env = vento();
  const body = (await env.runString(templateContent, data)).content;
  console.log("notification:", body);
  return body;
}

export interface JobsFound {
  searchCriteriaId: string,
  jobIds: string[]
}

export interface Events {
  publishCustSearchCriteriaCreated(payload: CustomerSearchCriteriaCreated[], topic:string): Promise<void>;
  publishJobsFound(payload: JobsFound[], topic: string): Promise<void>;
}

export class LiveQstash implements Events {
  private client: QstashClient;

  constructor() {
    load();
    console.log("Initializing Live Qstash")
    this.client = new QstashClient({ 
      token: Deno.env.get("QSTASH_KEY")!,
      baseUrl: Deno.env.get("QSTASH_URL")!
    } );
  }

  async publishCustSearchCriteriaCreated(payload: CustomerSearchCriteriaCreated[], topic: string): Promise<void> {

    console.log("sending message to Qstash");

    try {
      const response = await this.client.batchJSON([
        {
          urlGroup: topic,
          body: payload
        }
      ]);

      console.log("response from Qstash: ", response);

    } catch (e) {
      console.error(e);
    }
  }

  async publishJobsFound(payload: JobsFound[], topic: string): Promise<void> {

    console.log("sending jobs found message to Qstash");

    try {
      const response = await this.client.batchJSON([
        {
          urlGroup: topic,
          body: payload
        }
      ]);

      console.log("response from Qstash: ", response);

    } catch (e) {
      console.error(e);
    }
  }

}

export async function isValidQstashRequest(headers: Headers, body: string): Promise<boolean> {
  const r = new Receiver({
    currentSigningKey: Deno.env.get("QSTASH_CURRENT_SIGNING_KEY")!,
    nextSigningKey: Deno.env.get("QSTASH_NEXT_SIGNING_KEY")!,
  });

  const isValid = await r
    .verify({
      signature: headers.get("Upstash-Signature")!,
      body: body,
    })
    .catch((err: Error) => {
      console.error(err);
      return false;
    });

  console.log("request is valid from Qstash");

  return isValid;
}

/*
 * Applysquad specific commons
 */

export interface CustomerSearchCriteriaCreated {
  customerSearchCriteriaId: string,
}

export interface JobBoard {
  id:string,
  key: string,
  url: string,
}

export interface CustomerSearchCriteria {
  id: string,
  search_date: string,
  job_board_id: string,
  customer_id: string,
  job_titles: string[],
  locations: string[],
  search_status: "NEW"|"COMPLETE"
}

export interface Job {
  id?: string,
  title: string,
  employer: string,
  location: string,
  description: string,
  url: string,
  job_type?: "FULLTIME" | "PARTTIME" | "CONTRACT" | "INTERNSHIP" | "TEMPORARY",
  job_status?: "OPEN" | "CLOSED",
  create_src?: "AGENT",
  account_required?: boolean,
  customer_apply?: boolean,
  languages?: string,
  pay_amount?: number,
  pay_currency?: string,
  pay_frequency?: string,
  src_id?: string,
  summary?: string,
  visa_required?: string,
  created_at?: string,
  modified_time?: string,
  modified_by?: string
}

export interface JobSearchResult {
  jobs: Job[],
  totalCount: number,
  hasMore: boolean
}

export interface Plan {
  customerId: string,
  productCode: PRODUCT_CODE,
  status: PLAN_STATUS
}

export interface Product {
  id: number,
  productCode: PRODUCT_CODE
}

export interface Customer {
  id: string,
  email: string,
  affiliateId: string|undefined|null,
  referredById: string|undefined|null,
  plans: Plan[]
}

export interface OnboardingSurvey {
  id: number,
  jobTitles: string[],
  locations: string[],
}

export enum PLAN_STATUS {
  OPEN = "OPEN",
  COMPLETE = "COMPLETE"
}

export enum PRODUCT_CODE {
    APPS_20 = "APPS_20",
    APPS_50 = "APPS_50",
    APPS_100 = "APPS_100",
    APPS_500 = "APPS_500",
    NETWORK_80 = "NETWORK_80",
    NETWORK_20 = "NETWORK_20",
    APPS_TRIAL_5 = "APPS_TRIAL_5",
    OTHER = "OTHER"
}

export enum JOB_BOARDS {
  THEIR_STACK = "THEIR_STACK"
}

export enum TOPICS {
  SEARCH_JOB_BOARD = "TOPIC_SEARCH_JOB_BOARD",
  MATCH_CUSTOMER_JOB = "TOPIC_MATCH_CUSTOMER_JOB"
}


/**
 * Edge Fuctions do not allow reading from filesystem,
 * so templates are stored inside a typescript file.
 */
export const TEMPLATES = {
  affiliateNotification:
    `<h4>Congratulations {{ name }}!</h4>
     <p>
        Applysquad recieved a signup through your referral link. 
        The tracking number is: {{ trackingId }}. As your referral
        purchases plans, you will be granted credit.
     </p>
     <p>
        Thank you for being part of Applysquad!
     </p>`,
  purchaseAdminNotification: 
    `<h4>A new payment has been received:</h4>
     <p>{{customer.email}} paid \${{payment.amount_in_pennies/100}} for {{payment.product}}.</p>
     <p>Linkedin: {{customer.linkedin_url}}</p>
     <p>Linkdin Premium: {{customer.linkedin_premium? 'Yes' : 'No'}}</p>
     <p>Whatsapp: {{customer.whatsapp_number}}</p>
     <p>Pain Points: {{customer.pain_points}}</p>
     <p>Other Pain Points: {{customer.other_pain_points}}</p>`

};

export async function getCustomerWithActiveJobPlans(supabase: SupabaseClient<Database>): Promise<Customer[]> {

  const { data, error } = await supabase
    .from("customers")
    .select("*, plans!inner(*, products!inner(product_code))")
    .eq("plans.status", "OPEN")
    // nested filters are tricky, can't get this working, so filter in code for now
    // .filter("plans.products.product_code", 'ilike', "APPS%")
    ;

  if (error) {
    console.error(error);
    throw error;
  }

  const customers: Customer[] = data.map((customer) => (
    {
      id: customer.id,
      email: customer.email,
      affiliateId: customer.affiliate_id,
      referredById: customer.referred_by_id,
      plans: customer.plans.map((plan) => ({
        customerId: customer.id,
        productCode: getEnumKey(PRODUCT_CODE, plan.products.product_code), 
        status: getEnumKey(PLAN_STATUS, plan.status)
      }))
    }
  ))
  .filter((customer) => customer.plans.some((plan) => ( plan.productCode.startsWith("APPS"))));

  console.log("customers:", customers);

  return customers;
}

export async function getCustomerSearchCriteria(id: string, supabase: SupabaseClient<Database>): Promise<CustomerSearchCriteria> {
  const { data, error } = await supabase
    .from("customer_search_criteria")
    .select("*")
    .eq("id", id)
    .single(); // Ensures only one record is returned

  if (error) {
    throw error; // Throw the error if the query fails
  }

  if (!data) {
    throw new Error(`CustomerSearchCriteria with id ${id} not found`);
  }

  return  { 
    id: data.id,
    search_date: data.search_date,
    job_board_id: data.job_board_id,
    customer_id: data.customer_id,
    job_titles: data.job_titles,
    locations: data.locations,
    search_status: data.search_status
  };
}

/**
 * Finds the specified OnboardingSurvey, and validates it can be used to
 * perform auto job-search. This requires
 *  1) a job title
 *  2) a location
 *  
 * @returns the onboarding survey, if it can be used for auto job searching
 */
export async function getSearchableOnboardingSurvey(
  customerId: string, 
  supabase: SupabaseClient<Database>): Promise<OnboardingSurvey|null> {

    const onboarding = await getOnboardingSurvey(customerId, supabase);

    if (onboarding && onboarding.jobTitles.length != 0 && onboarding.locations.length != 0) {
      return onboarding;
    } else {
      return null;
    }
  }

export async function getOnboardingSurvey(
    customerId: string, 
    supabase: SupabaseClient<Database>): Promise<OnboardingSurvey|null> {

  const { data, error } = await supabase
    .from("customer_onboarding")
    .select("*, locations:onboarding_locations(*)")
    .eq("customer_id", customerId);

  if (error) {
    throw error;
  }

  if (!data) {
    console.log(`OnboardingSurvey with customer id ${customerId} not found`);
    return null;
  }

  const onboarding:OnboardingSurvey = {
    id: data[0].id,
    jobTitles: data[0].job_titles?.split(",") || [],
    locations: data[0].locations.map((location) => (
      location.location
    ))
  };

  return onboarding;

}

export async function getRelevantJobBoards(
  _onboarding: OnboardingSurvey,
  supabase: SupabaseClient<Database>): Promise<JobBoard[]> {

  // for now, just return all job boards
  // in the future doing a match against industry, work type, etc
  // may be a nice optimization
  const { data, error } = await supabase
    .from("job_boards")
    .select("*");
  if (error) {
    throw error;
  }
  return data.map((board) => ({
    id: board.id,
    key: board.key,
    url: board.url
  }));

}

export async function getExistingSearchCriteria(
  customerId: string,
  searchDate: Date,
  jobBoardId: string,
  supabase: SupabaseClient<Database>
): Promise<CustomerSearchCriteria|null> {

  const { data, error } = await supabase
    .from("customer_search_criteria")
    .select("*")
    .eq("customer_id", customerId)
    .eq("search_date", dateStr(searchDate))
    .eq("job_board_id", jobBoardId)
    .eq("search_status", "NEW");

  if (error) {
    throw error;
  }

  return data[0];
}

/**
 * Save jobs to the database
 */
export async function saveJobs(jobs: Job[], supabase: SupabaseClient<Database>): Promise<void> {
  if (jobs.length === 0) {
    console.log("No jobs to save");
    return;
  }

  const { error } = await supabase
    .from("jobs")
    .upsert(jobs, {
      onConflict: "src_id",
      ignoreDuplicates: false
    });

  if (error) {
    console.error("Error saving jobs:", error);
    throw error;
  }

  console.log(`Successfully saved ${jobs.length} jobs to database`);
}

/**
 * Update search criteria status
 */
export async function updateSearchCriteriaStatus(
  searchCriteriaId: string,
  status: "NEW" | "COMPLETE",
  supabase: SupabaseClient<Database>
): Promise<void> {
  const { error } = await supabase
    .from("customer_search_criteria")
    .update({ search_status: status })
    .eq("id", searchCriteriaId);

  if (error) {
    console.error("Error updating search criteria status:", error);
    throw error;
  }

  console.log(`Updated search criteria ${searchCriteriaId} status to ${status}`);
}

/**
 * Get job board by key
 */
export async function getJobBoardByKey(key: string, supabase: SupabaseClient<Database>): Promise<JobBoard | null> {
  const { data, error } = await supabase
    .from("job_boards")
    .select("*")
    .eq("key", key)
    .single();

  if (error) {
    console.error("Error fetching job board:", error);
    return null;
  }

  return data ? {
    id: data.id,
    key: data.key,
    url: data.url
  } : null;
}