declare module "#build/app-component.mjs";
declare module "#build/nitro.client.mjs";
declare module "#build/plugins.client.mjs";
declare module "#build/css.mjs";
declare module "#build/fetch.mjs";
declare module "#build/error-component.mjs";
declare module "#build/layouts.mjs";
declare module "#build/middleware.mjs";
declare module "#build/nuxt.config.mjs";
declare module "#build/paths.mjs";
declare module "#build/root-component.mjs";
declare module "#build/plugins.server.mjs";
declare module "#build/test-component-wrapper.mjs";
declare module "#build/color-mode-options.mjs";
declare module "#build/devtools/settings.mjs";
declare module "#build/routes.mjs";
declare module "#build/pages.mjs";
declare module "#build/router.options.mjs";
declare module "#build/unhead-plugins.mjs";
declare module "#build/unhead.config.mjs";
declare module "#build/components.plugin.mjs";
declare module "#build/component-names.mjs";
declare module "#build/components.islands.mjs";
declare module "#build/tailwind/expose/theme/fontFamily.mjs";
declare module "#build/tailwind/expose/theme/aspectRatio.mjs";
declare module "#build/tailwind/expose/theme/typography.mjs";
declare module "#build/tailwind/expose/theme/containers.mjs";
declare module "#build/tailwind/expose/theme/accentColor.mjs";
declare module "#build/tailwind/expose/theme/animation.mjs";
declare module "#build/tailwind/expose/theme/aria.mjs";
declare module "#build/tailwind/expose/theme/backdropBlur.mjs";
declare module "#build/tailwind/expose/theme/backdropBrightness.mjs";
declare module "#build/tailwind/expose/theme/backdropContrast.mjs";
declare module "#build/tailwind/expose/theme/backdropGrayscale.mjs";
declare module "#build/tailwind/expose/theme/backdropHueRotate.mjs";
declare module "#build/tailwind/expose/theme/backdropInvert.mjs";
declare module "#build/tailwind/expose/theme/backdropOpacity.mjs";
declare module "#build/tailwind/expose/theme/backdropSaturate.mjs";
declare module "#build/tailwind/expose/theme/backdropSepia.mjs";
declare module "#build/tailwind/expose/theme/backgroundColor.mjs";
declare module "#build/tailwind/expose/theme/backgroundImage.mjs";
declare module "#build/tailwind/expose/theme/backgroundOpacity.mjs";
declare module "#build/tailwind/expose/theme/backgroundPosition.mjs";
declare module "#build/tailwind/expose/theme/backgroundSize.mjs";
declare module "#build/tailwind/expose/theme/blur.mjs";
declare module "#build/tailwind/expose/theme/borderColor.mjs";
declare module "#build/tailwind/expose/theme/borderOpacity.mjs";
declare module "#build/tailwind/expose/theme/borderRadius.mjs";
declare module "#build/tailwind/expose/theme/borderSpacing.mjs";
declare module "#build/tailwind/expose/theme/borderWidth.mjs";
declare module "#build/tailwind/expose/theme/boxShadow.mjs";
declare module "#build/tailwind/expose/theme/boxShadowColor.mjs";
declare module "#build/tailwind/expose/theme/brightness.mjs";
declare module "#build/tailwind/expose/theme/caretColor.mjs";
declare module "#build/tailwind/expose/theme/colors.mjs";
declare module "#build/tailwind/expose/theme/columns.mjs";
declare module "#build/tailwind/expose/theme/container.mjs";
declare module "#build/tailwind/expose/theme/content.mjs";
declare module "#build/tailwind/expose/theme/contrast.mjs";
declare module "#build/tailwind/expose/theme/cursor.mjs";
declare module "#build/tailwind/expose/theme/divideColor.mjs";
declare module "#build/tailwind/expose/theme/divideOpacity.mjs";
declare module "#build/tailwind/expose/theme/divideWidth.mjs";
declare module "#build/tailwind/expose/theme/dropShadow.mjs";
declare module "#build/tailwind/expose/theme/fill.mjs";
declare module "#build/tailwind/expose/theme/flex.mjs";
declare module "#build/tailwind/expose/theme/flexBasis.mjs";
declare module "#build/tailwind/expose/theme/flexGrow.mjs";
declare module "#build/tailwind/expose/theme/flexShrink.mjs";
declare module "#build/tailwind/expose/theme/fontSize.mjs";
declare module "#build/tailwind/expose/theme/fontWeight.mjs";
declare module "#build/tailwind/expose/theme/gap.mjs";
declare module "#build/tailwind/expose/theme/gradientColorStops.mjs";
declare module "#build/tailwind/expose/theme/gradientColorStopPositions.mjs";
declare module "#build/tailwind/expose/theme/grayscale.mjs";
declare module "#build/tailwind/expose/theme/gridAutoColumns.mjs";
declare module "#build/tailwind/expose/theme/gridAutoRows.mjs";
declare module "#build/tailwind/expose/theme/gridColumn.mjs";
declare module "#build/tailwind/expose/theme/gridColumnEnd.mjs";
declare module "#build/tailwind/expose/theme/gridColumnStart.mjs";
declare module "#build/tailwind/expose/theme/gridRow.mjs";
declare module "#build/tailwind/expose/theme/gridRowEnd.mjs";
declare module "#build/tailwind/expose/theme/gridRowStart.mjs";
declare module "#build/tailwind/expose/theme/gridTemplateColumns.mjs";
declare module "#build/tailwind/expose/theme/gridTemplateRows.mjs";
declare module "#build/tailwind/expose/theme/height.mjs";
declare module "#build/tailwind/expose/theme/hueRotate.mjs";
declare module "#build/tailwind/expose/theme/inset.mjs";
declare module "#build/tailwind/expose/theme/invert.mjs";
declare module "#build/tailwind/expose/theme/keyframes.mjs";
declare module "#build/tailwind/expose/theme/letterSpacing.mjs";
declare module "#build/tailwind/expose/theme/lineHeight.mjs";
declare module "#build/tailwind/expose/theme/listStyleType.mjs";
declare module "#build/tailwind/expose/theme/listStyleImage.mjs";
declare module "#build/tailwind/expose/theme/margin.mjs";
declare module "#build/tailwind/expose/theme/lineClamp.mjs";
declare module "#build/tailwind/expose/theme/maxHeight.mjs";
declare module "#build/tailwind/expose/theme/maxWidth.mjs";
declare module "#build/tailwind/expose/theme/minHeight.mjs";
declare module "#build/tailwind/expose/theme/minWidth.mjs";
declare module "#build/tailwind/expose/theme/objectPosition.mjs";
declare module "#build/tailwind/expose/theme/opacity.mjs";
declare module "#build/tailwind/expose/theme/order.mjs";
declare module "#build/tailwind/expose/theme/outlineColor.mjs";
declare module "#build/tailwind/expose/theme/outlineOffset.mjs";
declare module "#build/tailwind/expose/theme/outlineWidth.mjs";
declare module "#build/tailwind/expose/theme/padding.mjs";
declare module "#build/tailwind/expose/theme/placeholderColor.mjs";
declare module "#build/tailwind/expose/theme/placeholderOpacity.mjs";
declare module "#build/tailwind/expose/theme/ringColor.mjs";
declare module "#build/tailwind/expose/theme/ringOffsetColor.mjs";
declare module "#build/tailwind/expose/theme/ringOffsetWidth.mjs";
declare module "#build/tailwind/expose/theme/ringOpacity.mjs";
declare module "#build/tailwind/expose/theme/ringWidth.mjs";
declare module "#build/tailwind/expose/theme/rotate.mjs";
declare module "#build/tailwind/expose/theme/saturate.mjs";
declare module "#build/tailwind/expose/theme/scale.mjs";
declare module "#build/tailwind/expose/theme/screens.mjs";
declare module "#build/tailwind/expose/theme/scrollMargin.mjs";
declare module "#build/tailwind/expose/theme/scrollPadding.mjs";
declare module "#build/tailwind/expose/theme/sepia.mjs";
declare module "#build/tailwind/expose/theme/skew.mjs";
declare module "#build/tailwind/expose/theme/space.mjs";
declare module "#build/tailwind/expose/theme/spacing.mjs";
declare module "#build/tailwind/expose/theme/stroke.mjs";
declare module "#build/tailwind/expose/theme/strokeWidth.mjs";
declare module "#build/tailwind/expose/theme/supports.mjs";
declare module "#build/tailwind/expose/theme/data.mjs";
declare module "#build/tailwind/expose/theme/textColor.mjs";
declare module "#build/tailwind/expose/theme/textDecorationColor.mjs";
declare module "#build/tailwind/expose/theme/textDecorationThickness.mjs";
declare module "#build/tailwind/expose/theme/textIndent.mjs";
declare module "#build/tailwind/expose/theme/textOpacity.mjs";
declare module "#build/tailwind/expose/theme/textUnderlineOffset.mjs";
declare module "#build/tailwind/expose/theme/transformOrigin.mjs";
declare module "#build/tailwind/expose/theme/transitionDelay.mjs";
declare module "#build/tailwind/expose/theme/transitionDuration.mjs";
declare module "#build/tailwind/expose/theme/transitionProperty.mjs";
declare module "#build/tailwind/expose/theme/transitionTimingFunction.mjs";
declare module "#build/tailwind/expose/theme/translate.mjs";
declare module "#build/tailwind/expose/theme/size.mjs";
declare module "#build/tailwind/expose/theme/width.mjs";
declare module "#build/tailwind/expose/theme/willChange.mjs";
declare module "#build/tailwind/expose/theme/zIndex.mjs";
declare module "#build/tailwind/expose/theme.mjs";
declare module "#build/tailwind/expose/corePlugins.mjs";
declare module "#build/tailwind/expose/plugins.mjs";
declare module "#build/tailwind/expose/content/relative.mjs";
declare module "#build/tailwind/expose/content/files.mjs";
declare module "#build/tailwind/expose/content/extract.mjs";
declare module "#build/tailwind/expose/content/transform.mjs";
declare module "#build/tailwind/expose/content.mjs";
declare module "#build/tailwind/expose/darkMode.mjs";
declare module "#build/tailwind/expose/safelist.mjs";
declare module "#build/tailwind/expose/variants/aspectRatio.mjs";
declare module "#build/tailwind/expose/variants.mjs";
declare module "#build/tailwind/expose/presets.mjs";
declare module "#build/tailwind/expose/prefix.mjs";
declare module "#build/tailwind/expose/important.mjs";
declare module "#build/tailwind/expose/separator.mjs";
declare module "#build/tailwind/expose/blocklist.mjs";
