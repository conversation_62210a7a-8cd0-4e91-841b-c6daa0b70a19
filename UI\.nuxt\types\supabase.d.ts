declare module '#supabase/server' {
  const serverSupabaseClient: typeof import('C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@nuxtjs/supabase/dist/runtime/server/services').serverSupabaseClient
  const serverSupabaseServiceRole: typeof import('C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@nuxtjs/supabase/dist/runtime/server/services').serverSupabaseServiceRole
  const serverSupabaseUser: typeof import('C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@nuxtjs/supabase/dist/runtime/server/services').serverSupabaseUser
  const serverSupabaseSession: typeof import('C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@nuxtjs/supabase/dist/runtime/server/services').serverSupabaseSession
}