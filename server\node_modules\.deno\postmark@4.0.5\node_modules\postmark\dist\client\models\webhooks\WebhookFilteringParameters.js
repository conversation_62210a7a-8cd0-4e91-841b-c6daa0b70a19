"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookFilteringParameters = void 0;
/**
 * Describes filtering parameters that can be used when retrieving webhooks.
 */
var WebhookFilteringParameters = /** @class */ (function () {
    function WebhookFilteringParameters(messageStream) {
        this.messageStream = messageStream;
    }
    return WebhookFilteringParameters;
}());
exports.WebhookFilteringParameters = WebhookFilteringParameters;
//# sourceMappingURL=WebhookFilteringParameters.js.map