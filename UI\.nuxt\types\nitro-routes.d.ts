// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/regenerate-cover-letter': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/regenerate-cover-letter').default>>>>
    }
    '/complete-email-setup': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/routes/complete-email-setup').default>>>>
    }
    '/init-email-setup': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/routes/init-email-setup').default>>>>
    }
    '/revoke-email-access': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/routes/revoke-email-access').default>>>>
    }
    '/__nuxt_error': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/nuxt/dist/core/runtime/nitro/renderer').default>>>>
    }
    '/api/_nuxt_icon/:collection': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../node_modules/@nuxt/icon/dist/runtime/server/api').default>>>>
    }
  }
}
export {}