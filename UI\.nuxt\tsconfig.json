// Generated by nuxi
{
  "compilerOptions": {
    "paths": {
      "nitropack/types": [
        "../node_modules/nitropack/types"
      ],
      "nitropack/runtime": [
        "../node_modules/nitropack/runtime"
      ],
      "nitropack": [
        "../node_modules/nitropack"
      ],
      "defu": [
        "../node_modules/defu"
      ],
      "h3": [
        "../node_modules/h3"
      ],
      "consola": [
        "../node_modules/consola"
      ],
      "ofetch": [
        "../node_modules/ofetch"
      ],
      "@unhead/vue": [
        "../node_modules/@unhead/vue"
      ],
      "@nuxt/devtools": [
        "../node_modules/@nuxt/devtools"
      ],
      "@vue/runtime-core": [
        "../node_modules/@vue/runtime-core"
      ],
      "@vue/compiler-sfc": [
        "../node_modules/@vue/compiler-sfc"
      ],
      "unplugin-vue-router/client": [
        "../node_modules/unplugin-vue-router/client"
      ],
      "@nuxt/schema": [
        "../node_modules/@nuxt/schema"
      ],
      "nuxt": [
        "../node_modules/nuxt"
      ],
      "vite/client": [
        "../node_modules/vite/client"
      ],
      "~": [
        ".."
      ],
      "~/*": [
        "../*"
      ],
      "@": [
        ".."
      ],
      "@/*": [
        "../*"
      ],
      "~~": [
        ".."
      ],
      "~~/*": [
        "../*"
      ],
      "@@": [
        ".."
      ],
      "@@/*": [
        "../*"
      ],
      "#shared": [
        "../shared"
      ],
      "assets": [
        "../assets"
      ],
      "assets/*": [
        "../assets/*"
      ],
      "public": [
        "../public"
      ],
      "public/*": [
        "../public/*"
      ],
      "#app": [
        "../node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "../node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "../node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "#ui": [
        "../node_modules/@nuxt/ui/dist/runtime"
      ],
      "#ui/*": [
        "../node_modules/@nuxt/ui/dist/runtime/*"
      ],
      "#ui-colors": [
        "./ui.colors"
      ],
      "#color-mode-options": [
        "./color-mode-options.mjs"
      ],
      "#vue-router": [
        "../node_modules/vue-router"
      ],
      "#imports": [
        "./imports"
      ],
      "#tailwind-config": [
        "./tailwind/expose"
      ],
      "#tailwind-config/*": [
        "./tailwind/expose/*"
      ],
      "vue-i18n": [
        "../node_modules/vue-i18n/dist/vue-i18n"
      ],
      "@intlify/shared": [
        "../node_modules/@intlify/shared/dist/shared"
      ],
      "@intlify/message-compiler": [
        "../node_modules/@intlify/message-compiler/dist/message-compiler"
      ],
      "@intlify/core-base": [
        "../node_modules/@intlify/core-base/dist/core-base"
      ],
      "@intlify/core": [
        "../node_modules/@intlify/core/dist/core.node"
      ],
      "@intlify/utils/h3": [
        "../node_modules/@intlify/utils/dist/h3"
      ],
      "ufo": [
        "../node_modules/ufo/dist/index"
      ],
      "#i18n": [
        "../node_modules/@nuxtjs/i18n/dist/runtime/composables/index"
      ],
      "#internal-i18n-types": [
        "../node_modules/@nuxtjs/i18n/dist/types"
      ],
      "#nuxt-i18n/logger": [
        "./nuxt-i18n-logger"
      ],
      "#app-manifest": [
        "./manifest/meta/dev"
      ],
      "#components": [
        "./components"
      ],
      "#build": [
        "."
      ],
      "#build/*": [
        "./*"
      ]
    },
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "ESNext",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,
    "strict": true,
    "noUncheckedIndexedAccess": false,
    "forceConsistentCasingInFileNames": true,
    "noImplicitOverride": true,
    "module": "preserve",
    "noEmit": true,
    "lib": [
      "ESNext",
      "dom",
      "dom.iterable",
      "webworker"
    ],
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "types": [],
    "moduleResolution": "Bundler",
    "useDefineForClassFields": true,
    "noImplicitThis": true,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "./nuxt.d.ts",
    "../.config/nuxt.*",
    "../**/*",
    "../node_modules/@nuxtjs/supabase/runtime",
    "../node_modules/@nuxtjs/supabase/dist/runtime",
    "../node_modules/@nuxt/icon/runtime",
    "../node_modules/@nuxt/icon/dist/runtime",
    "../node_modules/@nuxtjs/color-mode/runtime",
    "../node_modules/@nuxtjs/color-mode/dist/runtime",
    "../node_modules/@nuxtjs/tailwindcss/runtime",
    "../node_modules/@nuxtjs/tailwindcss/dist/runtime",
    "../node_modules/@nuxt/ui/runtime",
    "../node_modules/@nuxt/ui/dist/runtime",
    "../node_modules/@nuxt/eslint/runtime",
    "../node_modules/@nuxt/eslint/dist/runtime",
    "../node_modules/@nuxtjs/i18n/runtime",
    "../node_modules/@nuxtjs/i18n/dist/runtime",
    "../node_modules/nuxt-security/runtime",
    "../node_modules/nuxt-security/dist/runtime",
    "../node_modules/@nuxt/devtools/runtime",
    "../node_modules/@nuxt/devtools/dist/runtime",
    "../node_modules/@nuxt/telemetry/runtime",
    "../node_modules/@nuxt/telemetry/dist/runtime",
    ".."
  ],
  "exclude": [
    "../dist",
    "../node_modules",
    "../../node_modules",
    "../node_modules/nuxt/node_modules",
    "../node_modules/@nuxtjs/supabase/node_modules",
    "../node_modules/@nuxt/icon/node_modules",
    "../node_modules/@nuxtjs/color-mode/node_modules",
    "../node_modules/@nuxtjs/tailwindcss/node_modules",
    "../node_modules/@nuxt/ui/node_modules",
    "../node_modules/@nuxt/eslint/node_modules",
    "../node_modules/@nuxtjs/i18n/node_modules",
    "../node_modules/nuxt-security/node_modules",
    "../node_modules/@nuxt/devtools/node_modules",
    "../node_modules/@nuxt/telemetry/node_modules",
    "../node_modules/@nuxtjs/supabase/runtime/server",
    "../node_modules/@nuxtjs/supabase/dist/runtime/server",
    "../node_modules/@nuxt/icon/runtime/server",
    "../node_modules/@nuxt/icon/dist/runtime/server",
    "../node_modules/@nuxtjs/color-mode/runtime/server",
    "../node_modules/@nuxtjs/color-mode/dist/runtime/server",
    "../node_modules/@nuxtjs/tailwindcss/runtime/server",
    "../node_modules/@nuxtjs/tailwindcss/dist/runtime/server",
    "../node_modules/@nuxt/ui/runtime/server",
    "../node_modules/@nuxt/ui/dist/runtime/server",
    "../node_modules/@nuxt/eslint/runtime/server",
    "../node_modules/@nuxt/eslint/dist/runtime/server",
    "../node_modules/@nuxtjs/i18n/runtime/server",
    "../node_modules/@nuxtjs/i18n/dist/runtime/server",
    "../node_modules/nuxt-security/runtime/server",
    "../node_modules/nuxt-security/dist/runtime/server",
    "../node_modules/@nuxt/devtools/runtime/server",
    "../node_modules/@nuxt/devtools/dist/runtime/server",
    "../node_modules/@nuxt/telemetry/runtime/server",
    "../node_modules/@nuxt/telemetry/dist/runtime/server",
    "../.output"
  ]
}