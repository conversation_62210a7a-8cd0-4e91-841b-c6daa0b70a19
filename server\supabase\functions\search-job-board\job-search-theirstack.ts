/**
 * TheirStack Job Board Integration
 * 
 * This module provides functionality to search for jobs on TheirStack
 * and normalize the response data into our Job interface.
 */

import { load } from 'jsr:@std/dotenv';

// Load environment variables
load();

// Import Job interface from common
import { Job } from '../shared/common.ts';

// TheirStack API response interfaces
interface TheirStackJob {
  id: string;
  title: string;
  company: {
    name: string;
    logo?: string;
  };
  location: string;
  description: string;
  url: string;
  salary?: {
    min?: number;
    max?: number;
    currency?: string;
  };
  employment_type?: string;
  posted_at?: string;
  tags?: string[];
}

interface TheirStackSearchResponse {
  jobs: TheirStackJob[];
  total: number;
  page: number;
  per_page: number;
  has_more: boolean;
}

interface SearchOptions {
  limit?: number;
  offset?: number;
  remote?: boolean;
  employment_type?: string[];
}

export class TheirStackClient {
  private apiKey: string;
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor() {
    this.apiKey = Deno.env.get("THEIRSTACK_API_KEY") || "";
    this.baseUrl = "https://api.theirstack.com/v1";
    this.defaultHeaders = {
      "Authorization": `Bearer ${this.apiKey}`,
      "Content-Type": "application/json",
      "User-Agent": "ApplySquad/1.0"
    };

    if (!this.apiKey) {
      console.warn("THEIRSTACK_API_KEY not found in environment variables");
    }
  }

  /**
   * Search for jobs on TheirStack
   */
  async searchJobs(
    jobTitles: string[],
    locations: string[],
    options: SearchOptions = {}
  ): Promise<{ jobs: Job[], totalCount: number, hasMore: boolean }> {
    try {
      const searchParams = this.buildSearchParams(jobTitles, locations, options);
      const url = `${this.baseUrl}/jobs/search?${searchParams}`;

      console.log(`Searching TheirStack with URL: ${url}`);

      const response = await fetch(url, {
        method: "GET",
        headers: this.defaultHeaders,
      });

      if (!response.ok) {
        throw new Error(`TheirStack API error: ${response.status} ${response.statusText}`);
      }

      const data: TheirStackSearchResponse = await response.json();
      
      // Convert TheirStack jobs to our Job interface
      const normalizedJobs = data.jobs.map(job => this.normalizeJob(job));

      return {
        jobs: normalizedJobs,
        totalCount: data.total,
        hasMore: data.has_more
      };

    } catch (error) {
      console.error("Error searching TheirStack:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to search TheirStack: ${errorMessage}`);
    }
  }

  /**
   * Build search parameters for TheirStack API
   */
  private buildSearchParams(
    jobTitles: string[],
    locations: string[],
    options: SearchOptions
  ): string {
    const params = new URLSearchParams();

    // Add job titles as query
    if (jobTitles.length > 0) {
      params.append("q", jobTitles.join(" OR "));
    }

    // Add locations
    if (locations.length > 0) {
      locations.forEach(location => {
        params.append("location", location);
      });
    }

    // Add pagination
    if (options.limit) {
      params.append("per_page", options.limit.toString());
    }
    if (options.offset) {
      params.append("page", Math.floor(options.offset / (options.limit || 20) + 1).toString());
    }

    // Add employment type filter
    if (options.employment_type && options.employment_type.length > 0) {
      options.employment_type.forEach(type => {
        params.append("employment_type", type);
      });
    }

    // Add remote filter
    if (options.remote) {
      params.append("remote", "true");
    }

    // Default sorting by posted date (newest first)
    params.append("sort", "posted_at");
    params.append("order", "desc");

    return params.toString();
  }

  /**
   * Convert TheirStack job format to our Job interface
   */
  private normalizeJob(theirStackJob: TheirStackJob): Job {
    // Map employment type to our enum
    let jobType: "FULLTIME" | "PARTTIME" | "CONTRACT" | "INTERNSHIP" | "TEMPORARY" | undefined;
    if (theirStackJob.employment_type) {
      const typeMap: Record<string, "FULLTIME" | "PARTTIME" | "CONTRACT" | "INTERNSHIP" | "TEMPORARY"> = {
        "full-time": "FULLTIME",
        "fulltime": "FULLTIME",
        "full_time": "FULLTIME",
        "part-time": "PARTTIME",
        "parttime": "PARTTIME",
        "part_time": "PARTTIME",
        "contract": "CONTRACT",
        "contractor": "CONTRACT",
        "internship": "INTERNSHIP",
        "intern": "INTERNSHIP",
        "temporary": "TEMPORARY",
        "temp": "TEMPORARY"
      };
      jobType = typeMap[theirStackJob.employment_type.toLowerCase()];
    }

    // Extract salary information
    let payAmount: number | undefined;
    let payCurrency: string | undefined;
    if (theirStackJob.salary) {
      const { min, max, currency = "USD" } = theirStackJob.salary;
      payAmount = min || max; // Use min if available, otherwise max
      payCurrency = currency;
    }

    return {
      id: `theirstack_${theirStackJob.id}`, // Prefix to avoid ID conflicts
      title: theirStackJob.title,
      employer: theirStackJob.company.name,
      location: theirStackJob.location,
      description: theirStackJob.description,
      url: theirStackJob.url,
      job_type: jobType,
      job_status: "OPEN",
      pay_amount: payAmount,
      pay_currency: payCurrency,
      src_id: theirStackJob.id,
      account_required: false, // Default value, could be enhanced
      customer_apply: true, // Default value, could be enhanced
      created_at: new Date().toISOString(),
      modified_time: new Date().toISOString()
    };
  }

  /**
   * Test the API connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: "GET",
        headers: this.defaultHeaders,
      });
      return response.ok;
    } catch (error) {
      console.error("TheirStack connection test failed:", error);
      return false;
    }
  }
}

// Export a default instance
export const theirStackClient = new TheirStackClient();

// Export the class for testing
export { TheirStackClient };
