import process from 'node:process';globalThis._importMeta_={url:import.meta.url,env:process.env};import { Server } from 'node:http';
import { resolve, dirname, join } from 'node:path';
import node<PERSON>rypto, { webcrypto } from 'node:crypto';
import { parentPort, threadId } from 'node:worker_threads';
import { getRequestHeader, setResponseStatus, setResponseHeader, send, getRequestHeaders, getRequestURL, getResponseHeader, setResponseHeaders, defineEventHandler, handleCacheHeaders, splitCookiesString, createEvent, fetchWithEvent, isEvent, eventHandler, getResponseStatus, setHeaders, sendRedirect, proxyRequest, createError, removeResponseHeader, getQuery as getQuery$1, handleCors, getRequestIP, readMultipartFormData, readBody, createApp, createRouter as createRouter$1, to<PERSON>ode<PERSON><PERSON><PERSON>, lazy<PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, set<PERSON><PERSON><PERSON>, get<PERSON>ead<PERSON>, getResponseStatusText } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/h3/dist/index.mjs';
import { StopWatch } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/stopwatch-node/dist/index.js';
import { generateText } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/ai/dist/index.mjs';
import { Buffer as Buffer$1 } from 'node:buffer';
import { anthropic } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@ai-sdk/anthropic/dist/index.mjs';
import { mistral } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@ai-sdk/mistral/dist/index.mjs';
import { openai } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@ai-sdk/openai/dist/index.mjs';
import { pdfToText } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/pdf-ts/dist/index.js';
import Nylas from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/nylas/lib/esm/nylas.js';
import { createServerClient, parseCookieHeader } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@supabase/ssr/dist/main/index.js';
import { getRequestDependencies, getPreloadLinks, getPrefetchLinks, createRenderer } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/vue-bundle-renderer/dist/runtime.mjs';
import { stringify, uneval } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/devalue/index.js';
import destr from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/destr/dist/index.mjs';
import { renderToString } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/vue/server-renderer/index.mjs';
import { propsToString, renderSSRHead } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@unhead/ssr/dist/index.mjs';
import { createServerHead as createServerHead$1, CapoPlugin } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/unhead/dist/index.mjs';
import { klona } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/klona/dist/index.mjs';
import defu, { defuFn, createDefu } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/defu/dist/defu.mjs';
import { snakeCase } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/scule/dist/index.mjs';
import { createHooks } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/hookable/dist/index.mjs';
import { createFetch, Headers as Headers$1 } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/ofetch/dist/node.mjs';
import { fetchNodeRequestHandler, callNodeRequestHandler } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/node-mock-http/dist/index.mjs';
import { readFile } from 'node:fs/promises';
import consola, { consola as consola$1 } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/consola/dist/index.mjs';
import * as _youch from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/youch-redist/dist/youch.mjs';
import { SourceMapConsumer } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/nitropack/node_modules/source-map/source-map.js';
import { AsyncLocalStorage } from 'node:async_hooks';
import { getContext } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/unctx/dist/index.mjs';
import { captureRawStackTrace, parseRawStackTrace } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/errx/dist/index.js';
import { isVNode, unref, version } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/vue/index.mjs';
import { toRouteMatcher, createRouter } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/radix3/dist/index.mjs';
import { createStorage, prefixStorage } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/unstorage/dist/index.mjs';
import unstorage_47drivers_47fs from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/unstorage/drivers/fs.mjs';
import unstorage_47drivers_47lru_45cache from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/unstorage/drivers/lru-cache.mjs';
import { basename } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@nuxt/icon/node_modules/pathe/dist/index.mjs';
import { getIcons } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@iconify/utils/lib/index.mjs';
import { hash as hash$1 } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/ohash/dist/index.mjs';
import { digest } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/nitropack/node_modules/ohash/dist/index.mjs';
import { collections } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/.nuxt/nuxt-icon-server-bundle.mjs';
import { FilterXSS } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/xss/lib/index.js';
import { defineHeadPlugin } from 'file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/node_modules/@unhead/shared/dist/index.mjs';

const HASH_RE = /#/g;
const AMPERSAND_RE = /&/g;
const SLASH_RE = /\//g;
const EQUAL_RE = /=/g;
const PLUS_RE = /\+/g;
const ENC_CARET_RE = /%5e/gi;
const ENC_BACKTICK_RE = /%60/gi;
const ENC_PIPE_RE = /%7c/gi;
const ENC_SPACE_RE = /%20/gi;
function encode(text) {
  return encodeURI("" + text).replace(ENC_PIPE_RE, "|");
}
function encodeQueryValue(input) {
  return encode(typeof input === "string" ? input : JSON.stringify(input)).replace(PLUS_RE, "%2B").replace(ENC_SPACE_RE, "+").replace(HASH_RE, "%23").replace(AMPERSAND_RE, "%26").replace(ENC_BACKTICK_RE, "`").replace(ENC_CARET_RE, "^").replace(SLASH_RE, "%2F");
}
function encodeQueryKey(text) {
  return encodeQueryValue(text).replace(EQUAL_RE, "%3D");
}
function decode(text = "") {
  try {
    return decodeURIComponent("" + text);
  } catch {
    return "" + text;
  }
}
function decodeQueryKey(text) {
  return decode(text.replace(PLUS_RE, " "));
}
function decodeQueryValue(text) {
  return decode(text.replace(PLUS_RE, " "));
}

function parseQuery(parametersString = "") {
  const object = {};
  if (parametersString[0] === "?") {
    parametersString = parametersString.slice(1);
  }
  for (const parameter of parametersString.split("&")) {
    const s = parameter.match(/([^=]+)=?(.*)/) || [];
    if (s.length < 2) {
      continue;
    }
    const key = decodeQueryKey(s[1]);
    if (key === "__proto__" || key === "constructor") {
      continue;
    }
    const value = decodeQueryValue(s[2] || "");
    if (object[key] === void 0) {
      object[key] = value;
    } else if (Array.isArray(object[key])) {
      object[key].push(value);
    } else {
      object[key] = [object[key], value];
    }
  }
  return object;
}
function encodeQueryItem(key, value) {
  if (typeof value === "number" || typeof value === "boolean") {
    value = String(value);
  }
  if (!value) {
    return encodeQueryKey(key);
  }
  if (Array.isArray(value)) {
    return value.map((_value) => `${encodeQueryKey(key)}=${encodeQueryValue(_value)}`).join("&");
  }
  return `${encodeQueryKey(key)}=${encodeQueryValue(value)}`;
}
function stringifyQuery(query) {
  return Object.keys(query).filter((k) => query[k] !== void 0).map((k) => encodeQueryItem(k, query[k])).filter(Boolean).join("&");
}

const PROTOCOL_STRICT_REGEX = /^[\s\w\0+.-]{2,}:([/\\]{1,2})/;
const PROTOCOL_REGEX = /^[\s\w\0+.-]{2,}:([/\\]{2})?/;
const PROTOCOL_RELATIVE_REGEX = /^([/\\]\s*){2,}[^/\\]/;
const JOIN_LEADING_SLASH_RE = /^\.?\//;
function hasProtocol(inputString, opts = {}) {
  if (typeof opts === "boolean") {
    opts = { acceptRelative: opts };
  }
  if (opts.strict) {
    return PROTOCOL_STRICT_REGEX.test(inputString);
  }
  return PROTOCOL_REGEX.test(inputString) || (opts.acceptRelative ? PROTOCOL_RELATIVE_REGEX.test(inputString) : false);
}
function hasTrailingSlash(input = "", respectQueryAndFragment) {
  {
    return input.endsWith("/");
  }
}
function withoutTrailingSlash(input = "", respectQueryAndFragment) {
  {
    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || "/";
  }
}
function withTrailingSlash(input = "", respectQueryAndFragment) {
  {
    return input.endsWith("/") ? input : input + "/";
  }
}
function withoutBase(input, base) {
  if (isEmptyURL(base)) {
    return input;
  }
  const _base = withoutTrailingSlash(base);
  if (!input.startsWith(_base)) {
    return input;
  }
  const trimmed = input.slice(_base.length);
  return trimmed[0] === "/" ? trimmed : "/" + trimmed;
}
function withQuery(input, query) {
  const parsed = parseURL(input);
  const mergedQuery = { ...parseQuery(parsed.search), ...query };
  parsed.search = stringifyQuery(mergedQuery);
  return stringifyParsedURL(parsed);
}
function getQuery(input) {
  return parseQuery(parseURL(input).search);
}
function isEmptyURL(url) {
  return !url || url === "/";
}
function isNonEmptyURL(url) {
  return url && url !== "/";
}
function joinURL(base, ...input) {
  let url = base || "";
  for (const segment of input.filter((url2) => isNonEmptyURL(url2))) {
    if (url) {
      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, "");
      url = withTrailingSlash(url) + _segment;
    } else {
      url = segment;
    }
  }
  return url;
}
function joinRelativeURL(..._input) {
  const JOIN_SEGMENT_SPLIT_RE = /\/(?!\/)/;
  const input = _input.filter(Boolean);
  const segments = [];
  let segmentsDepth = 0;
  for (const i of input) {
    if (!i || i === "/") {
      continue;
    }
    for (const [sindex, s] of i.split(JOIN_SEGMENT_SPLIT_RE).entries()) {
      if (!s || s === ".") {
        continue;
      }
      if (s === "..") {
        if (segments.length === 1 && hasProtocol(segments[0])) {
          continue;
        }
        segments.pop();
        segmentsDepth--;
        continue;
      }
      if (sindex === 1 && segments[segments.length - 1]?.endsWith(":/")) {
        segments[segments.length - 1] += "/" + s;
        continue;
      }
      segments.push(s);
      segmentsDepth++;
    }
  }
  let url = segments.join("/");
  if (segmentsDepth >= 0) {
    if (input[0]?.startsWith("/") && !url.startsWith("/")) {
      url = "/" + url;
    } else if (input[0]?.startsWith("./") && !url.startsWith("./")) {
      url = "./" + url;
    }
  } else {
    url = "../".repeat(-1 * segmentsDepth) + url;
  }
  if (input[input.length - 1]?.endsWith("/") && !url.endsWith("/")) {
    url += "/";
  }
  return url;
}

const protocolRelative = Symbol.for("ufo:protocolRelative");
function parseURL(input = "", defaultProto) {
  const _specialProtoMatch = input.match(
    /^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i
  );
  if (_specialProtoMatch) {
    const [, _proto, _pathname = ""] = _specialProtoMatch;
    return {
      protocol: _proto.toLowerCase(),
      pathname: _pathname,
      href: _proto + _pathname,
      auth: "",
      host: "",
      search: "",
      hash: ""
    };
  }
  if (!hasProtocol(input, { acceptRelative: true })) {
    return parsePath(input);
  }
  const [, protocol = "", auth, hostAndPath = ""] = input.replace(/\\/g, "/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/) || [];
  let [, host = "", path = ""] = hostAndPath.match(/([^#/?]*)(.*)?/) || [];
  if (protocol === "file:") {
    path = path.replace(/\/(?=[A-Za-z]:)/, "");
  }
  const { pathname, search, hash } = parsePath(path);
  return {
    protocol: protocol.toLowerCase(),
    auth: auth ? auth.slice(0, Math.max(0, auth.length - 1)) : "",
    host,
    pathname,
    search,
    hash,
    [protocolRelative]: !protocol
  };
}
function parsePath(input = "") {
  const [pathname = "", search = "", hash = ""] = (input.match(/([^#?]*)(\?[^#]*)?(#.*)?/) || []).splice(1);
  return {
    pathname,
    search,
    hash
  };
}
function stringifyParsedURL(parsed) {
  const pathname = parsed.pathname || "";
  const search = parsed.search ? (parsed.search.startsWith("?") ? "" : "?") + parsed.search : "";
  const hash = parsed.hash || "";
  const auth = parsed.auth ? parsed.auth + "@" : "";
  const host = parsed.host || "";
  const proto = parsed.protocol || parsed[protocolRelative] ? (parsed.protocol || "") + "//" : "";
  return proto + auth + host + pathname + search + hash;
}

function isJsonRequest(event) {
  if (hasReqHeader(event, "accept", "text/html")) {
    return false;
  }
  return hasReqHeader(event, "accept", "application/json") || hasReqHeader(event, "user-agent", "curl/") || hasReqHeader(event, "user-agent", "httpie/") || hasReqHeader(event, "sec-fetch-mode", "cors") || event.path.startsWith("/api/") || event.path.endsWith(".json");
}
function hasReqHeader(event, name, includes) {
  const value = getRequestHeader(event, name);
  return value && typeof value === "string" && value.toLowerCase().includes(includes);
}
function normalizeError(error, isDev) {
  const cwd = typeof process.cwd === "function" ? process.cwd() : "/";
  const stack = (error.unhandled || error.fatal) ? [] : (error.stack || "").split("\n").splice(1).filter((line) => line.includes("at ")).map((line) => {
    const text = line.replace(cwd + "/", "./").replace("webpack:/", "").replace("file://", "").trim();
    return {
      text,
      internal: line.includes("node_modules") && !line.includes(".cache") || line.includes("internal") || line.includes("new Promise")
    };
  });
  const statusCode = error.statusCode || 500;
  const statusMessage = error.statusMessage ?? (statusCode === 404 ? "Not Found" : "");
  const message = error.unhandled ? "internal server error" : error.message || error.toString();
  return {
    stack,
    statusCode,
    statusMessage,
    message
  };
}

const errorHandler$0 = (async function errorhandler(error, event) {
  const { stack, statusCode, statusMessage, message } = normalizeError(error);
  const errorObject = {
    url: event.path,
    statusCode,
    statusMessage,
    message,
    stack: statusCode !== 404 ? `<pre>${stack.map((i) => `<span class="stack${i.internal ? " internal" : ""}">${i.text}</span>`).join("\n")}</pre>` : "",
    // TODO: check and validate error.data for serialisation into query
    data: error.data
  };
  if (error.unhandled || error.fatal) {
    const tags = [
      "[nuxt]",
      "[request error]",
      error.unhandled && "[unhandled]",
      error.fatal && "[fatal]",
      Number(errorObject.statusCode) !== 200 && `[${errorObject.statusCode}]`
    ].filter(Boolean).join(" ");
    console.error(tags, (error.message || error.toString() || "internal server error") + "\n" + stack.map((l) => "  " + l.text).join("  \n"));
  }
  if (event.handled) {
    return;
  }
  setResponseStatus(event, errorObject.statusCode !== 200 && errorObject.statusCode || 500, errorObject.statusMessage);
  if (isJsonRequest(event)) {
    setResponseHeader(event, "Content-Type", "application/json");
    return send(event, JSON.stringify(errorObject));
  }
  const reqHeaders = getRequestHeaders(event);
  const isRenderingError = event.path.startsWith("/__nuxt_error") || !!reqHeaders["x-nuxt-error"];
  const res = isRenderingError ? null : await useNitroApp().localFetch(
    withQuery(joinURL(useRuntimeConfig(event).app.baseURL, "/__nuxt_error"), errorObject),
    {
      headers: { ...reqHeaders, "x-nuxt-error": "true" },
      redirect: "manual"
    }
  ).catch(() => null);
  if (!res) {
    const { template } = await Promise.resolve().then(function () { return errorDev; }) ;
    {
      errorObject.description = errorObject.message;
    }
    if (event.handled) {
      return;
    }
    setResponseHeader(event, "Content-Type", "text/html;charset=UTF-8");
    return send(event, template(errorObject));
  }
  const html = await res.text();
  if (event.handled) {
    return;
  }
  for (const [header, value] of res.headers.entries()) {
    setResponseHeader(event, header, value);
  }
  setResponseStatus(event, res.status && res.status !== 200 ? res.status : void 0, res.statusText);
  return send(event, html);
});

function defineNitroErrorHandler(handler) {
  return handler;
}

const { Youch, ErrorParser } = _youch;
const errorHandler$1 = defineNitroErrorHandler(
  async function defaultNitroErrorHandler(error, event) {
    const res = await defaultHandler(error, event);
    setResponseHeaders(event, res.headers);
    setResponseStatus(event, res.status, res.statusText);
    return send(
      event,
      typeof res.body === "string" ? res.body : JSON.stringify(res.body, null, 2)
    );
  }
);
async function defaultHandler(error, event, opts) {
  const isSensitive = error.unhandled || error.fatal;
  const statusCode = error.statusCode || 500;
  const statusMessage = error.statusMessage || "Server Error";
  const url = getRequestURL(event, { xForwardedHost: true, xForwardedProto: true });
  if (statusCode === 404) {
    const baseURL = "/";
    if (/^\/[^/]/.test(baseURL) && !url.pathname.startsWith(baseURL)) {
      const redirectTo = `${baseURL}${url.pathname.slice(1)}${url.search}`;
      return {
        status: 302,
        statusText: "Found",
        headers: { location: redirectTo },
        body: `Redirecting...`
      };
    }
  }
  await loadStackTrace(error).catch(consola.error);
  const youch = new Youch();
  if (isSensitive && !opts?.silent) {
    const tags = [error.unhandled && "[unhandled]", error.fatal && "[fatal]"].filter(Boolean).join(" ");
    const ansiError = await (await youch.toANSI(error)).replaceAll(process.cwd(), ".");
    consola.error(
      `[request error] ${tags} [${event.method}] ${url}

`,
      ansiError
    );
  }
  const useJSON = opts?.json || !getRequestHeader(event, "accept")?.includes("text/html");
  const headers = {
    "content-type": useJSON ? "application/json" : "text/html",
    // Prevent browser from guessing the MIME types of resources.
    "x-content-type-options": "nosniff",
    // Prevent error page from being embedded in an iframe
    "x-frame-options": "DENY",
    // Prevent browsers from sending the Referer header
    "referrer-policy": "no-referrer",
    // Disable the execution of any js
    "content-security-policy": "script-src 'self' 'unsafe-inline'; object-src 'none'; base-uri 'self';"
  };
  if (statusCode === 404 || !getResponseHeader(event, "cache-control")) {
    headers["cache-control"] = "no-cache";
  }
  if (!globalThis.crypto && !useJSON) {
    globalThis.crypto = nodeCrypto;
  }
  const body = useJSON ? {
    error: true,
    url,
    statusCode,
    statusMessage,
    message: error.message,
    data: error.data,
    stack: error.stack?.split("\n").map((line) => line.trim())
  } : await youch.toHTML(error, {
    request: {
      url: url.href,
      method: event.method,
      headers: getRequestHeaders(event)
    }
  });
  return {
    status: statusCode,
    statusText: statusMessage,
    headers,
    body
  };
}
async function loadStackTrace(error) {
  if (!(error instanceof Error)) {
    return;
  }
  const parsed = await new ErrorParser().defineSourceLoader(sourceLoader).parse(error);
  const stack = error.message + "\n" + parsed.frames.map((frame) => fmtFrame(frame)).join("\n");
  Object.defineProperty(error, "stack", { value: stack });
  if (error.cause) {
    await loadStackTrace(error.cause).catch(consola.error);
  }
}
async function sourceLoader(frame) {
  if (!frame.fileName || frame.fileType !== "fs" || frame.type === "native") {
    return;
  }
  if (frame.type === "app") {
    const rawSourceMap = await readFile(`${frame.fileName}.map`, "utf8").catch(() => {
    });
    if (rawSourceMap) {
      const consumer = await new SourceMapConsumer(rawSourceMap);
      const originalPosition = consumer.originalPositionFor({ line: frame.lineNumber, column: frame.columnNumber });
      if (originalPosition.source && originalPosition.line) {
        frame.fileName = resolve(dirname(frame.fileName), originalPosition.source);
        frame.lineNumber = originalPosition.line;
        frame.columnNumber = originalPosition.column || 0;
      }
    }
  }
  const contents = await readFile(frame.fileName, "utf8").catch(() => {
  });
  return contents ? { contents } : void 0;
}
function fmtFrame(frame) {
  if (frame.type === "native") {
    return frame.raw;
  }
  const src = `${frame.fileName || ""}:${frame.lineNumber}:${frame.columnNumber})`;
  return frame.functionName ? `at ${frame.functionName} (${src}` : `at ${src}`;
}

const errorHandlers = [errorHandler$0, errorHandler$1];

async function errorHandler(error, event) {
  for (const handler of errorHandlers) {
    try {
      await handler(error, event, { defaultHandler });
      if (event.handled) {
        return; // Response handled
      }
    } catch(error) {
      // Handler itself thrown, log and continue
      console.error(error);
    }
  }
  // H3 will handle fallback
}

const script$1 = `
if (!window.__NUXT_DEVTOOLS_TIME_METRIC__) {
  Object.defineProperty(window, '__NUXT_DEVTOOLS_TIME_METRIC__', {
    value: {},
    enumerable: false,
    configurable: true,
  })
}
window.__NUXT_DEVTOOLS_TIME_METRIC__.appInit = Date.now()
`;

const _5d6SKuf4ChrJuBLa1CGU5qeV1hBVVPFJzLGpGTUfxc4 = (function(nitro) {
  nitro.hooks.hook("render:html", (htmlContext) => {
    htmlContext.head.push(`<script>${script$1}<\/script>`);
  });
});

const rootDir = "C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI";

const appHead = {"meta":[{"name":"viewport","content":"width=device-width, initial-scale=1"},{"charset":"utf-8"}],"link":[],"style":[],"script":[],"noscript":[]};

const appRootTag = "div";

const appRootAttrs = {"id":"__nuxt"};

const appTeleportTag = "div";

const appTeleportAttrs = {"id":"teleports"};

const appId = "nuxt-app";

const devReducers = {
  VNode: (data) => isVNode(data) ? { type: data.type, props: data.props } : void 0,
  URL: (data) => data instanceof URL ? data.toString() : void 0
};
const asyncContext = getContext("nuxt-dev", { asyncContext: true, AsyncLocalStorage });
const _xmO_yjnWQmfJEHn8xg7CV2D9ejRzj2WzvhUIuUBxQJs = (nitroApp) => {
  const handler = nitroApp.h3App.handler;
  nitroApp.h3App.handler = (event) => {
    return asyncContext.callAsync({ logs: [], event }, () => handler(event));
  };
  onConsoleLog((_log) => {
    const ctx = asyncContext.tryUse();
    if (!ctx) {
      return;
    }
    const rawStack = captureRawStackTrace();
    if (!rawStack || rawStack.includes("runtime/vite-node.mjs")) {
      return;
    }
    const trace = [];
    let filename = "";
    for (const entry of parseRawStackTrace(rawStack)) {
      if (entry.source === globalThis._importMeta_.url) {
        continue;
      }
      if (EXCLUDE_TRACE_RE.test(entry.source)) {
        continue;
      }
      filename ||= entry.source.replace(withTrailingSlash(rootDir), "");
      trace.push({
        ...entry,
        source: entry.source.startsWith("file://") ? entry.source.replace("file://", "") : entry.source
      });
    }
    const log = {
      ..._log,
      // Pass along filename to allow the client to display more info about where log comes from
      filename,
      // Clean up file names in stack trace
      stack: trace
    };
    ctx.logs.push(log);
  });
  nitroApp.hooks.hook("afterResponse", () => {
    const ctx = asyncContext.tryUse();
    if (!ctx) {
      return;
    }
    return nitroApp.hooks.callHook("dev:ssr-logs", { logs: ctx.logs, path: ctx.event.path });
  });
  nitroApp.hooks.hook("render:html", (htmlContext) => {
    const ctx = asyncContext.tryUse();
    if (!ctx) {
      return;
    }
    try {
      const reducers = Object.assign(/* @__PURE__ */ Object.create(null), devReducers, ctx.event.context._payloadReducers);
      htmlContext.bodyAppend.unshift(`<script type="application/json" data-nuxt-logs="${appId}">${stringify(ctx.logs, reducers)}<\/script>`);
    } catch (e) {
      const shortError = e instanceof Error && "toString" in e ? ` Received \`${e.toString()}\`.` : "";
      console.warn(`[nuxt] Failed to stringify dev server logs.${shortError} You can define your own reducer/reviver for rich types following the instructions in https://nuxt.com/docs/api/composables/use-nuxt-app#payload.`);
    }
  });
};
const EXCLUDE_TRACE_RE = /\/node_modules\/(?:.*\/)?(?:nuxt|nuxt-nightly|nuxt-edge|nuxt3|consola|@vue)\/|core\/runtime\/nitro/;
function onConsoleLog(callback) {
  consola$1.addReporter({
    log(logObj) {
      callback(logObj);
    }
  });
  consola$1.wrapConsole();
}

const script = "\"use strict\";(()=>{const t=window,e=document.documentElement,c=[\"dark\",\"light\"],n=getStorageValue(\"localStorage\",\"nuxt-color-mode\")||\"light\";let i=n===\"system\"?u():n;const r=e.getAttribute(\"data-color-mode-forced\");r&&(i=r),l(i),t[\"__NUXT_COLOR_MODE__\"]={preference:n,value:i,getColorScheme:u,addColorScheme:l,removeColorScheme:d};function l(o){const s=\"\"+o+\"\",a=\"light\";e.classList?e.classList.add(s):e.className+=\" \"+s,a&&e.setAttribute(\"data-\"+a,o)}function d(o){const s=\"\"+o+\"\",a=\"light\";e.classList?e.classList.remove(s):e.className=e.className.replace(new RegExp(s,\"g\"),\"\"),a&&e.removeAttribute(\"data-\"+a)}function f(o){return t.matchMedia(\"(prefers-color-scheme\"+o+\")\")}function u(){if(t.matchMedia&&f(\"\").media!==\"not all\"){for(const o of c)if(f(\":\"+o).matches)return o}return\"light\"}})();function getStorageValue(t,e){switch(t){case\"localStorage\":return window.localStorage.getItem(e);case\"sessionStorage\":return window.sessionStorage.getItem(e);case\"cookie\":return getCookie(e);default:return null}}function getCookie(t){const c=(\"; \"+window.document.cookie).split(\"; \"+t+\"=\");if(c.length===2)return c.pop()?.split(\";\").shift()}";

const _ps4b82qqg1u2yyEUlr_JnACLJr_CPL4jS219btWWKrY = (function(nitro) {
  nitro.hooks.hook("render:html", (htmlContext) => {
    htmlContext.head.push(`<script>${script}<\/script>`);
  });
});

const defineAppConfig = (config) => config;

const appConfig0 = defineAppConfig({
  ui: {
    primary: "orange",
    gray: "cool",
    tabs: {
      list: {
        tab: {
          active: "ring-1 ring-gray-300 "
        }
      }
    },
    accordion: {
      default: {
        class: "ring-1 ring-gray-300 bg-gray-100 text-gray-700"
      }
    },
    notifications: {
      // Show toasts at the top right of the screen
      position: "top-0 bottom-[unset]"
    }
  }
});

const inlineAppConfig = {
  "nuxt": {},
  "icon": {
    "provider": "server",
    "class": "",
    "aliases": {},
    "iconifyApiEndpoint": "https://api.iconify.design",
    "localApiEndpoint": "/api/_nuxt_icon",
    "fallbackToApi": true,
    "cssSelectorPrefix": "i-",
    "cssWherePseudo": true,
    "mode": "css",
    "attrs": {
      "aria-hidden": true
    },
    "collections": [
      "academicons",
      "akar-icons",
      "ant-design",
      "arcticons",
      "basil",
      "bi",
      "bitcoin-icons",
      "bpmn",
      "brandico",
      "bx",
      "bxl",
      "bxs",
      "bytesize",
      "carbon",
      "catppuccin",
      "cbi",
      "charm",
      "ci",
      "cib",
      "cif",
      "cil",
      "circle-flags",
      "circum",
      "clarity",
      "codicon",
      "covid",
      "cryptocurrency",
      "cryptocurrency-color",
      "dashicons",
      "devicon",
      "devicon-plain",
      "ei",
      "el",
      "emojione",
      "emojione-monotone",
      "emojione-v1",
      "entypo",
      "entypo-social",
      "eos-icons",
      "ep",
      "et",
      "eva",
      "f7",
      "fa",
      "fa-brands",
      "fa-regular",
      "fa-solid",
      "fa6-brands",
      "fa6-regular",
      "fa6-solid",
      "fad",
      "fe",
      "feather",
      "file-icons",
      "flag",
      "flagpack",
      "flat-color-icons",
      "flat-ui",
      "flowbite",
      "fluent",
      "fluent-emoji",
      "fluent-emoji-flat",
      "fluent-emoji-high-contrast",
      "fluent-mdl2",
      "fontelico",
      "fontisto",
      "formkit",
      "foundation",
      "fxemoji",
      "gala",
      "game-icons",
      "geo",
      "gg",
      "gis",
      "gravity-ui",
      "gridicons",
      "grommet-icons",
      "guidance",
      "healthicons",
      "heroicons",
      "heroicons-outline",
      "heroicons-solid",
      "hugeicons",
      "humbleicons",
      "ic",
      "icomoon-free",
      "icon-park",
      "icon-park-outline",
      "icon-park-solid",
      "icon-park-twotone",
      "iconamoon",
      "iconoir",
      "icons8",
      "il",
      "ion",
      "iwwa",
      "jam",
      "la",
      "lets-icons",
      "line-md",
      "logos",
      "ls",
      "lucide",
      "lucide-lab",
      "mage",
      "majesticons",
      "maki",
      "map",
      "marketeq",
      "material-symbols",
      "material-symbols-light",
      "mdi",
      "mdi-light",
      "medical-icon",
      "memory",
      "meteocons",
      "mi",
      "mingcute",
      "mono-icons",
      "mynaui",
      "nimbus",
      "nonicons",
      "noto",
      "noto-v1",
      "octicon",
      "oi",
      "ooui",
      "openmoji",
      "oui",
      "pajamas",
      "pepicons",
      "pepicons-pencil",
      "pepicons-pop",
      "pepicons-print",
      "ph",
      "pixelarticons",
      "prime",
      "ps",
      "quill",
      "radix-icons",
      "raphael",
      "ri",
      "rivet-icons",
      "si-glyph",
      "simple-icons",
      "simple-line-icons",
      "skill-icons",
      "solar",
      "streamline",
      "streamline-emojis",
      "subway",
      "svg-spinners",
      "system-uicons",
      "tabler",
      "tdesign",
      "teenyicons",
      "token",
      "token-branded",
      "topcoat",
      "twemoji",
      "typcn",
      "uil",
      "uim",
      "uis",
      "uit",
      "uiw",
      "unjs",
      "vaadin",
      "vs",
      "vscode-icons",
      "websymbol",
      "weui",
      "whh",
      "wi",
      "wpf",
      "zmdi",
      "zondicons"
    ],
    "fetchTimeout": 1500
  },
  "ui": {
    "primary": "green",
    "gray": "cool",
    "colors": [
      "red",
      "orange",
      "amber",
      "yellow",
      "lime",
      "green",
      "emerald",
      "teal",
      "cyan",
      "sky",
      "blue",
      "indigo",
      "violet",
      "purple",
      "fuchsia",
      "pink",
      "rose",
      "primary"
    ],
    "strategy": "merge"
  }
};

const appConfig = defuFn(appConfig0, inlineAppConfig);

function getEnv(key, opts) {
  const envKey = snakeCase(key).toUpperCase();
  return destr(
    process.env[opts.prefix + envKey] ?? process.env[opts.altPrefix + envKey]
  );
}
function _isObject(input) {
  return typeof input === "object" && !Array.isArray(input);
}
function applyEnv(obj, opts, parentKey = "") {
  for (const key in obj) {
    const subKey = parentKey ? `${parentKey}_${key}` : key;
    const envValue = getEnv(subKey, opts);
    if (_isObject(obj[key])) {
      if (_isObject(envValue)) {
        obj[key] = { ...obj[key], ...envValue };
        applyEnv(obj[key], opts, subKey);
      } else if (envValue === void 0) {
        applyEnv(obj[key], opts, subKey);
      } else {
        obj[key] = envValue ?? obj[key];
      }
    } else {
      obj[key] = envValue ?? obj[key];
    }
    if (opts.envExpansion && typeof obj[key] === "string") {
      obj[key] = _expandFromEnv(obj[key]);
    }
  }
  return obj;
}
const envExpandRx = /\{\{([^{}]*)\}\}/g;
function _expandFromEnv(value) {
  return value.replace(envExpandRx, (match, key) => {
    return process.env[key] || match;
  });
}

const _inlineRuntimeConfig = {
  "app": {
    "baseURL": "/",
    "buildId": "dev",
    "buildAssetsDir": "/_nuxt/",
    "cdnURL": ""
  },
  "nitro": {
    "envPrefix": "NUXT_",
    "routeRules": {
      "/__nuxt_error": {
        "cache": false
      },
      "/confirm": {
        "ssr": false
      },
      "/**": {
        "headers": {
          "Referrer-Policy": "no-referrer",
          "Strict-Transport-Security": "max-age=15552000; includeSubDomains;",
          "X-Content-Type-Options": "nosniff",
          "X-Download-Options": "noopen",
          "X-Frame-Options": "SAMEORIGIN",
          "X-Permitted-Cross-Domain-Policies": "none",
          "X-XSS-Protection": "0"
        }
      },
      "/_nuxt/builds/meta/**": {
        "headers": {
          "cache-control": "public, max-age=31536000, immutable"
        }
      },
      "/_nuxt/builds/**": {
        "headers": {
          "cache-control": "public, max-age=1, immutable"
        }
      }
    }
  },
  "public": {
    "SUPABASE_URL": "http://127.0.0.1:54321",
    "SUPABASE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0",
    "SITE_URL": "http://localhost:3000",
    "NYLAS_API_URL": "https://api.nylas.com",
    "NYLAS_API_KEY": "your-nylas-api-key",
    "NYLAS_CLIENT_ID": "your-nylas-client-id",
    "EMAIL_SETUP_CALLBACK_URL": "/complete-email-setup",
    "supabase": {
      "url": "http://127.0.0.1:54321",
      "key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0",
      "redirect": true,
      "redirectOptions": {
        "login": "/login",
        "callback": "/confirm",
        "exclude": [
          "/*"
        ],
        "cookieRedirect": false,
        "include": [
          "/customer",
          "/agent",
          "/admin"
        ]
      },
      "cookieName": "sb",
      "cookieOptions": {
        "maxAge": 28800,
        "sameSite": "lax",
        "secure": true
      },
      "clientOptions": {}
    },
    "i18n": {
      "baseUrl": "",
      "defaultLocale": "en",
      "defaultDirection": "ltr",
      "strategy": "prefix_except_default",
      "lazy": false,
      "rootRedirect": "",
      "routesNameSeparator": "___",
      "defaultLocaleRouteNameSuffix": "default",
      "skipSettingLocaleOnNavigate": false,
      "differentDomains": false,
      "trailingSlash": false,
      "locales": [
        {
          "code": "en",
          "name": "English",
          "files": [
            {
              "path": "C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/i18n/locales/en.json",
              "cache": ""
            }
          ]
        }
      ],
      "detectBrowserLanguage": {
        "alwaysRedirect": false,
        "cookieCrossOrigin": false,
        "cookieDomain": "",
        "cookieKey": "i18n_redirected",
        "cookieSecure": false,
        "fallbackLocale": "",
        "redirectOn": "root",
        "useCookie": true
      },
      "experimental": {
        "localeDetector": "",
        "switchLocalePathLinkSSR": false,
        "autoImportTranslationFunctions": false,
        "typedPages": true,
        "typedOptionsAndMessages": false,
        "generatedLocaleFilePathFormat": "absolute",
        "alternateLinkCanonicalQueries": false,
        "hmr": true
      },
      "multiDomainLocales": false
    }
  },
  "supabase": {
    "serviceKey": ""
  },
  "icon": {
    "serverKnownCssClasses": []
  },
  "private": {
    "basicAuth": false
  },
  "security": {
    "strict": false,
    "headers": {
      "crossOriginResourcePolicy": "same-origin",
      "crossOriginOpenerPolicy": "same-origin",
      "crossOriginEmbedderPolicy": "unsafe-none",
      "contentSecurityPolicy": {
        "base-uri": [
          "'none'"
        ],
        "font-src": [
          "'self'",
          "https:",
          "data:"
        ],
        "form-action": [
          "'self'"
        ],
        "frame-ancestors": [
          "'self'"
        ],
        "img-src": [
          "'self'",
          "data:"
        ],
        "object-src": [
          "'none'"
        ],
        "script-src-attr": [
          "'none'"
        ],
        "style-src": [
          "'self'",
          "https:",
          "'unsafe-inline'"
        ],
        "script-src": [
          "'self'",
          "https:",
          "'unsafe-inline'",
          "'strict-dynamic'",
          "'nonce-{{nonce}}'"
        ],
        "upgrade-insecure-requests": true
      },
      "originAgentCluster": "?1",
      "referrerPolicy": "no-referrer",
      "strictTransportSecurity": {
        "maxAge": 15552000,
        "includeSubdomains": true
      },
      "xContentTypeOptions": "nosniff",
      "xDNSPrefetchControl": "off",
      "xDownloadOptions": "noopen",
      "xFrameOptions": "SAMEORIGIN",
      "xPermittedCrossDomainPolicies": "none",
      "xXSSProtection": "0",
      "permissionsPolicy": {
        "camera": [],
        "display-capture": [],
        "fullscreen": [],
        "geolocation": [],
        "microphone": []
      }
    },
    "requestSizeLimiter": {
      "maxRequestSizeInBytes": 2000000,
      "maxUploadFileRequestInBytes": 8000000,
      "throwError": true
    },
    "rateLimiter": {
      "tokensPerInterval": 150,
      "interval": 300000,
      "headers": false,
      "driver": {
        "name": "lruCache"
      },
      "whiteList": "",
      "throwError": true
    },
    "xssValidator": {
      "methods": [
        "GET",
        "POST"
      ],
      "throwError": true
    },
    "corsHandler": {
      "origin": "http://localhost:3000",
      "methods": [
        "GET",
        "HEAD",
        "PUT",
        "PATCH",
        "POST",
        "DELETE"
      ],
      "preflight": {
        "statusCode": 204
      }
    },
    "allowedMethodsRestricter": {
      "methods": "*",
      "throwError": true
    },
    "hidePoweredBy": true,
    "enabled": true,
    "csrf": false,
    "nonce": true,
    "removeLoggers": true,
    "ssg": {
      "meta": true,
      "hashScripts": true,
      "hashStyles": false,
      "nitroHeaders": true,
      "exportToPresets": true
    },
    "sri": true
  }
};
const envOptions = {
  prefix: "NITRO_",
  altPrefix: _inlineRuntimeConfig.nitro.envPrefix ?? process.env.NITRO_ENV_PREFIX ?? "_",
  envExpansion: _inlineRuntimeConfig.nitro.envExpansion ?? process.env.NITRO_ENV_EXPANSION ?? false
};
const _sharedRuntimeConfig = _deepFreeze(
  applyEnv(klona(_inlineRuntimeConfig), envOptions)
);
function useRuntimeConfig(event) {
  if (!event) {
    return _sharedRuntimeConfig;
  }
  if (event.context.nitro.runtimeConfig) {
    return event.context.nitro.runtimeConfig;
  }
  const runtimeConfig = klona(_inlineRuntimeConfig);
  applyEnv(runtimeConfig, envOptions);
  event.context.nitro.runtimeConfig = runtimeConfig;
  return runtimeConfig;
}
const _sharedAppConfig = _deepFreeze(klona(appConfig));
function useAppConfig(event) {
  {
    return _sharedAppConfig;
  }
}
function _deepFreeze(object) {
  const propNames = Object.getOwnPropertyNames(object);
  for (const name of propNames) {
    const value = object[name];
    if (value && typeof value === "object") {
      _deepFreeze(value);
    }
  }
  return Object.freeze(object);
}
new Proxy(/* @__PURE__ */ Object.create(null), {
  get: (_, prop) => {
    console.warn(
      "Please use `useRuntimeConfig()` instead of accessing config directly."
    );
    const runtimeConfig = useRuntimeConfig();
    if (prop in runtimeConfig) {
      return runtimeConfig[prop];
    }
    return void 0;
  }
});

function defineNitroPlugin(def) {
  return def;
}

const serverAssets = [{"baseName":"server","dir":"C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/server/assets"}];

const assets = createStorage();

for (const asset of serverAssets) {
  assets.mount(asset.baseName, unstorage_47drivers_47fs({ base: asset.dir, ignore: (asset?.ignore || []) }));
}

const storage$1 = createStorage({});

storage$1.mount('/assets', assets);

storage$1.mount('#rate-limiter-storage', unstorage_47drivers_47lru_45cache({"driver":"lruCache"}));
storage$1.mount('root', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI"}));
storage$1.mount('src', unstorage_47drivers_47fs({"driver":"fs","readOnly":true,"base":"C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/server"}));
storage$1.mount('build', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/.nuxt"}));
storage$1.mount('cache', unstorage_47drivers_47fs({"driver":"fs","readOnly":false,"base":"C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/.nuxt/cache"}));
storage$1.mount('data', unstorage_47drivers_47fs({"driver":"fs","base":"C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/.data/kv"}));

function useStorage(base = "") {
  return base ? prefixStorage(storage$1, base) : storage$1;
}

function hash(value) {
  return digest(typeof value === "string" ? value : serialize(value)).replace(/[-_]/g, "").slice(0, 10);
}
function serialize(object) {
  const hasher = new Hasher();
  hasher.dispatch(object);
  return hasher.buff;
}
class Hasher {
  buff = "";
  #context = /* @__PURE__ */ new Map();
  write(str) {
    this.buff += str;
  }
  dispatch(value) {
    const type = value === null ? "null" : typeof value;
    return this[type](value);
  }
  object(object) {
    if (object && typeof object.toJSON === "function") {
      return this.object(object.toJSON());
    }
    const objString = Object.prototype.toString.call(object);
    let objType = "";
    const objectLength = objString.length;
    objType = objectLength < 10 ? "unknown:[" + objString + "]" : objString.slice(8, objectLength - 1);
    objType = objType.toLowerCase();
    let objectNumber = null;
    if ((objectNumber = this.#context.get(object)) === void 0) {
      this.#context.set(object, this.#context.size);
    } else {
      return this.dispatch("[CIRCULAR:" + objectNumber + "]");
    }
    if (typeof Buffer !== "undefined" && Buffer.isBuffer && Buffer.isBuffer(object)) {
      this.write("buffer:");
      return this.write(object.toString("utf8"));
    }
    if (objType !== "object" && objType !== "function" && objType !== "asyncfunction") {
      if (this[objType]) {
        this[objType](object);
      } else {
        this.unknown(object, objType);
      }
    } else {
      const keys = Object.keys(object).sort();
      const extraKeys = [];
      this.write("object:" + (keys.length + extraKeys.length) + ":");
      const dispatchForKey = (key) => {
        this.dispatch(key);
        this.write(":");
        this.dispatch(object[key]);
        this.write(",");
      };
      for (const key of keys) {
        dispatchForKey(key);
      }
      for (const key of extraKeys) {
        dispatchForKey(key);
      }
    }
  }
  array(arr, unordered) {
    unordered = unordered === void 0 ? false : unordered;
    this.write("array:" + arr.length + ":");
    if (!unordered || arr.length <= 1) {
      for (const entry of arr) {
        this.dispatch(entry);
      }
      return;
    }
    const contextAdditions = /* @__PURE__ */ new Map();
    const entries = arr.map((entry) => {
      const hasher = new Hasher();
      hasher.dispatch(entry);
      for (const [key, value] of hasher.#context) {
        contextAdditions.set(key, value);
      }
      return hasher.toString();
    });
    this.#context = contextAdditions;
    entries.sort();
    return this.array(entries, false);
  }
  date(date) {
    return this.write("date:" + date.toJSON());
  }
  symbol(sym) {
    return this.write("symbol:" + sym.toString());
  }
  unknown(value, type) {
    this.write(type);
    if (!value) {
      return;
    }
    this.write(":");
    if (value && typeof value.entries === "function") {
      return this.array(
        [...value.entries()],
        true
        /* ordered */
      );
    }
  }
  error(err) {
    return this.write("error:" + err.toString());
  }
  boolean(bool) {
    return this.write("bool:" + bool);
  }
  string(string) {
    this.write("string:" + string.length + ":");
    this.write(string);
  }
  function(fn) {
    this.write("fn:");
    if (isNativeFunction(fn)) {
      this.dispatch("[native]");
    } else {
      this.dispatch(fn.toString());
    }
  }
  number(number) {
    return this.write("number:" + number);
  }
  null() {
    return this.write("Null");
  }
  undefined() {
    return this.write("Undefined");
  }
  regexp(regex) {
    return this.write("regex:" + regex.toString());
  }
  arraybuffer(arr) {
    this.write("arraybuffer:");
    return this.dispatch(new Uint8Array(arr));
  }
  url(url) {
    return this.write("url:" + url.toString());
  }
  map(map) {
    this.write("map:");
    const arr = [...map];
    return this.array(arr, false);
  }
  set(set) {
    this.write("set:");
    const arr = [...set];
    return this.array(arr, false);
  }
  bigint(number) {
    return this.write("bigint:" + number.toString());
  }
}
for (const type of [
  "uint8array",
  "uint8clampedarray",
  "unt8array",
  "uint16array",
  "unt16array",
  "uint32array",
  "unt32array",
  "float32array",
  "float64array"
]) {
  Hasher.prototype[type] = function(arr) {
    this.write(type + ":");
    return this.array([...arr], false);
  };
}
const nativeFunc = "[native code] }";
const nativeFuncLength = nativeFunc.length;
function isNativeFunction(f) {
  if (typeof f !== "function") {
    return false;
  }
  return Function.prototype.toString.call(f).slice(-nativeFuncLength) === nativeFunc;
}

function defaultCacheOptions() {
  return {
    name: "_",
    base: "/cache",
    swr: true,
    maxAge: 1
  };
}
function defineCachedFunction(fn, opts = {}) {
  opts = { ...defaultCacheOptions(), ...opts };
  const pending = {};
  const group = opts.group || "nitro/functions";
  const name = opts.name || fn.name || "_";
  const integrity = opts.integrity || hash([fn, opts]);
  const validate = opts.validate || ((entry) => entry.value !== void 0);
  async function get(key, resolver, shouldInvalidateCache, event) {
    const cacheKey = [opts.base, group, name, key + ".json"].filter(Boolean).join(":").replace(/:\/$/, ":index");
    let entry = await useStorage().getItem(cacheKey).catch((error) => {
      console.error(`[cache] Cache read error.`, error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }) || {};
    if (typeof entry !== "object") {
      entry = {};
      const error = new Error("Malformed data read from cache.");
      console.error("[cache]", error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }
    const ttl = (opts.maxAge ?? 0) * 1e3;
    if (ttl) {
      entry.expires = Date.now() + ttl;
    }
    const expired = shouldInvalidateCache || entry.integrity !== integrity || ttl && Date.now() - (entry.mtime || 0) > ttl || validate(entry) === false;
    const _resolve = async () => {
      const isPending = pending[key];
      if (!isPending) {
        if (entry.value !== void 0 && (opts.staleMaxAge || 0) >= 0 && opts.swr === false) {
          entry.value = void 0;
          entry.integrity = void 0;
          entry.mtime = void 0;
          entry.expires = void 0;
        }
        pending[key] = Promise.resolve(resolver());
      }
      try {
        entry.value = await pending[key];
      } catch (error) {
        if (!isPending) {
          delete pending[key];
        }
        throw error;
      }
      if (!isPending) {
        entry.mtime = Date.now();
        entry.integrity = integrity;
        delete pending[key];
        if (validate(entry) !== false) {
          let setOpts;
          if (opts.maxAge && !opts.swr) {
            setOpts = { ttl: opts.maxAge };
          }
          const promise = useStorage().setItem(cacheKey, entry, setOpts).catch((error) => {
            console.error(`[cache] Cache write error.`, error);
            useNitroApp().captureError(error, { event, tags: ["cache"] });
          });
          if (event?.waitUntil) {
            event.waitUntil(promise);
          }
        }
      }
    };
    const _resolvePromise = expired ? _resolve() : Promise.resolve();
    if (entry.value === void 0) {
      await _resolvePromise;
    } else if (expired && event && event.waitUntil) {
      event.waitUntil(_resolvePromise);
    }
    if (opts.swr && validate(entry) !== false) {
      _resolvePromise.catch((error) => {
        console.error(`[cache] SWR handler error.`, error);
        useNitroApp().captureError(error, { event, tags: ["cache"] });
      });
      return entry;
    }
    return _resolvePromise.then(() => entry);
  }
  return async (...args) => {
    const shouldBypassCache = await opts.shouldBypassCache?.(...args);
    if (shouldBypassCache) {
      return fn(...args);
    }
    const key = await (opts.getKey || getKey)(...args);
    const shouldInvalidateCache = await opts.shouldInvalidateCache?.(...args);
    const entry = await get(
      key,
      () => fn(...args),
      shouldInvalidateCache,
      args[0] && isEvent(args[0]) ? args[0] : void 0
    );
    let value = entry.value;
    if (opts.transform) {
      value = await opts.transform(entry, ...args) || value;
    }
    return value;
  };
}
function cachedFunction(fn, opts = {}) {
  return defineCachedFunction(fn, opts);
}
function getKey(...args) {
  return args.length > 0 ? hash(args) : "";
}
function escapeKey(key) {
  return String(key).replace(/\W/g, "");
}
function defineCachedEventHandler(handler, opts = defaultCacheOptions()) {
  const variableHeaderNames = (opts.varies || []).filter(Boolean).map((h) => h.toLowerCase()).sort();
  const _opts = {
    ...opts,
    getKey: async (event) => {
      const customKey = await opts.getKey?.(event);
      if (customKey) {
        return escapeKey(customKey);
      }
      const _path = event.node.req.originalUrl || event.node.req.url || event.path;
      let _pathname;
      try {
        _pathname = escapeKey(decodeURI(parseURL(_path).pathname)).slice(0, 16) || "index";
      } catch {
        _pathname = "-";
      }
      const _hashedPath = `${_pathname}.${hash(_path)}`;
      const _headers = variableHeaderNames.map((header) => [header, event.node.req.headers[header]]).map(([name, value]) => `${escapeKey(name)}.${hash(value)}`);
      return [_hashedPath, ..._headers].join(":");
    },
    validate: (entry) => {
      if (!entry.value) {
        return false;
      }
      if (entry.value.code >= 400) {
        return false;
      }
      if (entry.value.body === void 0) {
        return false;
      }
      if (entry.value.headers.etag === "undefined" || entry.value.headers["last-modified"] === "undefined") {
        return false;
      }
      return true;
    },
    group: opts.group || "nitro/handlers",
    integrity: opts.integrity || hash([handler, opts])
  };
  const _cachedHandler = cachedFunction(
    async (incomingEvent) => {
      const variableHeaders = {};
      for (const header of variableHeaderNames) {
        const value = incomingEvent.node.req.headers[header];
        if (value !== void 0) {
          variableHeaders[header] = value;
        }
      }
      const reqProxy = cloneWithProxy(incomingEvent.node.req, {
        headers: variableHeaders
      });
      const resHeaders = {};
      let _resSendBody;
      const resProxy = cloneWithProxy(incomingEvent.node.res, {
        statusCode: 200,
        writableEnded: false,
        writableFinished: false,
        headersSent: false,
        closed: false,
        getHeader(name) {
          return resHeaders[name];
        },
        setHeader(name, value) {
          resHeaders[name] = value;
          return this;
        },
        getHeaderNames() {
          return Object.keys(resHeaders);
        },
        hasHeader(name) {
          return name in resHeaders;
        },
        removeHeader(name) {
          delete resHeaders[name];
        },
        getHeaders() {
          return resHeaders;
        },
        end(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2();
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return this;
        },
        write(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2(void 0);
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return true;
        },
        writeHead(statusCode, headers2) {
          this.statusCode = statusCode;
          if (headers2) {
            if (Array.isArray(headers2) || typeof headers2 === "string") {
              throw new TypeError("Raw headers  is not supported.");
            }
            for (const header in headers2) {
              const value = headers2[header];
              if (value !== void 0) {
                this.setHeader(
                  header,
                  value
                );
              }
            }
          }
          return this;
        }
      });
      const event = createEvent(reqProxy, resProxy);
      event.fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: useNitroApp().localFetch
      });
      event.$fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: globalThis.$fetch
      });
      event.waitUntil = incomingEvent.waitUntil;
      event.context = incomingEvent.context;
      event.context.cache = {
        options: _opts
      };
      const body = await handler(event) || _resSendBody;
      const headers = event.node.res.getHeaders();
      headers.etag = String(
        headers.Etag || headers.etag || `W/"${hash(body)}"`
      );
      headers["last-modified"] = String(
        headers["Last-Modified"] || headers["last-modified"] || (/* @__PURE__ */ new Date()).toUTCString()
      );
      const cacheControl = [];
      if (opts.swr) {
        if (opts.maxAge) {
          cacheControl.push(`s-maxage=${opts.maxAge}`);
        }
        if (opts.staleMaxAge) {
          cacheControl.push(`stale-while-revalidate=${opts.staleMaxAge}`);
        } else {
          cacheControl.push("stale-while-revalidate");
        }
      } else if (opts.maxAge) {
        cacheControl.push(`max-age=${opts.maxAge}`);
      }
      if (cacheControl.length > 0) {
        headers["cache-control"] = cacheControl.join(", ");
      }
      const cacheEntry = {
        code: event.node.res.statusCode,
        headers,
        body
      };
      return cacheEntry;
    },
    _opts
  );
  return defineEventHandler(async (event) => {
    if (opts.headersOnly) {
      if (handleCacheHeaders(event, { maxAge: opts.maxAge })) {
        return;
      }
      return handler(event);
    }
    const response = await _cachedHandler(
      event
    );
    if (event.node.res.headersSent || event.node.res.writableEnded) {
      return response.body;
    }
    if (handleCacheHeaders(event, {
      modifiedTime: new Date(response.headers["last-modified"]),
      etag: response.headers.etag,
      maxAge: opts.maxAge
    })) {
      return;
    }
    event.node.res.statusCode = response.code;
    for (const name in response.headers) {
      const value = response.headers[name];
      if (name === "set-cookie") {
        event.node.res.appendHeader(
          name,
          splitCookiesString(value)
        );
      } else {
        if (value !== void 0) {
          event.node.res.setHeader(name, value);
        }
      }
    }
    return response.body;
  });
}
function cloneWithProxy(obj, overrides) {
  return new Proxy(obj, {
    get(target, property, receiver) {
      if (property in overrides) {
        return overrides[property];
      }
      return Reflect.get(target, property, receiver);
    },
    set(target, property, value, receiver) {
      if (property in overrides) {
        overrides[property] = value;
        return true;
      }
      return Reflect.set(target, property, value, receiver);
    }
  });
}
const cachedEventHandler = defineCachedEventHandler;

function defineRenderHandler(render) {
  const runtimeConfig = useRuntimeConfig();
  return eventHandler(async (event) => {
    const nitroApp = useNitroApp();
    const ctx = { event, render, response: void 0 };
    await nitroApp.hooks.callHook("render:before", ctx);
    if (!ctx.response) {
      if (event.path === `${runtimeConfig.app.baseURL}favicon.ico`) {
        setResponseHeader(event, "Content-Type", "image/x-icon");
        return send(
          event,
          "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
        );
      }
      ctx.response = await ctx.render(event);
      if (!ctx.response) {
        const _currentStatus = getResponseStatus(event);
        setResponseStatus(event, _currentStatus === 200 ? 500 : _currentStatus);
        return send(
          event,
          "No response returned from render handler: " + event.path
        );
      }
    }
    await nitroApp.hooks.callHook("render:response", ctx.response, ctx);
    if (ctx.response.headers) {
      setResponseHeaders(event, ctx.response.headers);
    }
    if (ctx.response.statusCode || ctx.response.statusMessage) {
      setResponseStatus(
        event,
        ctx.response.statusCode,
        ctx.response.statusMessage
      );
    }
    return ctx.response.body;
  });
}

const config = useRuntimeConfig();
const _routeRulesMatcher = toRouteMatcher(
  createRouter({ routes: config.nitro.routeRules })
);
function createRouteRulesHandler(ctx) {
  return eventHandler((event) => {
    const routeRules = getRouteRules(event);
    if (routeRules.headers) {
      setHeaders(event, routeRules.headers);
    }
    if (routeRules.redirect) {
      let target = routeRules.redirect.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.redirect._redirectStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return sendRedirect(event, target, routeRules.redirect.statusCode);
    }
    if (routeRules.proxy) {
      let target = routeRules.proxy.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.proxy._proxyStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery(event.path);
        target = withQuery(target, query);
      }
      return proxyRequest(event, target, {
        fetch: ctx.localFetch,
        ...routeRules.proxy
      });
    }
  });
}
function getRouteRules(event) {
  event.context._nitro = event.context._nitro || {};
  if (!event.context._nitro.routeRules) {
    event.context._nitro.routeRules = getRouteRulesForPath(
      withoutBase(event.path.split("?")[0], useRuntimeConfig().app.baseURL)
    );
  }
  return event.context._nitro.routeRules;
}
function getRouteRulesForPath(path) {
  return defu({}, ..._routeRulesMatcher.matchAll(path).reverse());
}

const r=Object.create(null),i=e=>globalThis.process?.env||globalThis._importMeta_.env||globalThis.Deno?.env.toObject()||globalThis.__env__||(e?r:globalThis),o=new Proxy(r,{get(e,s){return i()[s]??r[s]},has(e,s){const E=i();return s in E||s in r},set(e,s,E){const B=i(true);return B[s]=E,true},deleteProperty(e,s){if(!s)return  false;const E=i(true);return delete E[s],true},ownKeys(){const e=i(true);return Object.keys(e)}}),t=typeof process<"u"&&process.env&&"development"||"",f=[["APPVEYOR"],["AWS_AMPLIFY","AWS_APP_ID",{ci:true}],["AZURE_PIPELINES","SYSTEM_TEAMFOUNDATIONCOLLECTIONURI"],["AZURE_STATIC","INPUT_AZURE_STATIC_WEB_APPS_API_TOKEN"],["APPCIRCLE","AC_APPCIRCLE"],["BAMBOO","bamboo_planKey"],["BITBUCKET","BITBUCKET_COMMIT"],["BITRISE","BITRISE_IO"],["BUDDY","BUDDY_WORKSPACE_ID"],["BUILDKITE"],["CIRCLE","CIRCLECI"],["CIRRUS","CIRRUS_CI"],["CLOUDFLARE_PAGES","CF_PAGES",{ci:true}],["CODEBUILD","CODEBUILD_BUILD_ARN"],["CODEFRESH","CF_BUILD_ID"],["DRONE"],["DRONE","DRONE_BUILD_EVENT"],["DSARI"],["GITHUB_ACTIONS"],["GITLAB","GITLAB_CI"],["GITLAB","CI_MERGE_REQUEST_ID"],["GOCD","GO_PIPELINE_LABEL"],["LAYERCI"],["HUDSON","HUDSON_URL"],["JENKINS","JENKINS_URL"],["MAGNUM"],["NETLIFY"],["NETLIFY","NETLIFY_LOCAL",{ci:false}],["NEVERCODE"],["RENDER"],["SAIL","SAILCI"],["SEMAPHORE"],["SCREWDRIVER"],["SHIPPABLE"],["SOLANO","TDDIUM"],["STRIDER"],["TEAMCITY","TEAMCITY_VERSION"],["TRAVIS"],["VERCEL","NOW_BUILDER"],["VERCEL","VERCEL",{ci:false}],["VERCEL","VERCEL_ENV",{ci:false}],["APPCENTER","APPCENTER_BUILD_ID"],["CODESANDBOX","CODESANDBOX_SSE",{ci:false}],["CODESANDBOX","CODESANDBOX_HOST",{ci:false}],["STACKBLITZ"],["STORMKIT"],["CLEAVR"],["ZEABUR"],["CODESPHERE","CODESPHERE_APP_ID",{ci:true}],["RAILWAY","RAILWAY_PROJECT_ID"],["RAILWAY","RAILWAY_SERVICE_ID"],["DENO-DEPLOY","DENO_DEPLOYMENT_ID"],["FIREBASE_APP_HOSTING","FIREBASE_APP_HOSTING",{ci:true}]];function b(){if(globalThis.process?.env)for(const e of f){const s=e[1]||e[0];if(globalThis.process?.env[s])return {name:e[0].toLowerCase(),...e[2]}}return globalThis.process?.env?.SHELL==="/bin/jsh"&&globalThis.process?.versions?.webcontainer?{name:"stackblitz",ci:false}:{name:"",ci:false}}const l=b();l.name;function n(e){return e?e!=="false":false}const I=globalThis.process?.platform||"",T=n(o.CI)||l.ci!==false,a=n(globalThis.process?.stdout&&globalThis.process?.stdout.isTTY);n(o.DEBUG);const R=t==="test"||n(o.TEST);n(o.MINIMAL)||T||R||!a;const A=/^win/i.test(I);!n(o.NO_COLOR)&&(n(o.FORCE_COLOR)||(a||A)&&o.TERM!=="dumb"||T);const C=(globalThis.process?.versions?.node||"").replace(/^v/,"")||null;Number(C?.split(".")[0])||null;const y=globalThis.process||Object.create(null),_={versions:{}};new Proxy(y,{get(e,s){if(s==="env")return o;if(s in e)return e[s];if(s in _)return _[s]}});const c=globalThis.process?.release?.name==="node",O=!!globalThis.Bun||!!globalThis.process?.versions?.bun,D=!!globalThis.Deno,L=!!globalThis.fastly,S=!!globalThis.Netlify,u=!!globalThis.EdgeRuntime,N=globalThis.navigator?.userAgent==="Cloudflare-Workers",F=[[S,"netlify"],[u,"edge-light"],[N,"workerd"],[L,"fastly"],[D,"deno"],[O,"bun"],[c,"node"]];function G(){const e=F.find(s=>s[0]);if(e)return {name:e[1]}}const P=G();P?.name||"";

const scheduledTasks = false;

const tasks = {
  
};

const __runningTasks__ = {};
async function runTask(name, {
  payload = {},
  context = {}
} = {}) {
  if (__runningTasks__[name]) {
    return __runningTasks__[name];
  }
  if (!(name in tasks)) {
    throw createError({
      message: `Task \`${name}\` is not available!`,
      statusCode: 404
    });
  }
  if (!tasks[name].resolve) {
    throw createError({
      message: `Task \`${name}\` is not implemented!`,
      statusCode: 501
    });
  }
  const handler = await tasks[name].resolve();
  const taskEvent = { name, payload, context };
  __runningTasks__[name] = handler.run(taskEvent);
  try {
    const res = await __runningTasks__[name];
    return res;
  } finally {
    delete __runningTasks__[name];
  }
}

function buildAssetsDir() {
  return useRuntimeConfig().app.buildAssetsDir;
}
function buildAssetsURL(...path) {
  return joinRelativeURL(publicAssetsURL(), buildAssetsDir(), ...path);
}
function publicAssetsURL(...path) {
  const app = useRuntimeConfig().app;
  const publicBase = app.cdnURL || app.baseURL;
  return path.length ? joinRelativeURL(publicBase, ...path) : publicBase;
}

const defuReplaceArray = createDefu((obj, key, value) => {
  if (Array.isArray(obj[key]) || Array.isArray(value)) {
    obj[key] = value;
    return true;
  }
});

const nitroAppSecurityOptions = {};
function getAppSecurityOptions() {
  return nitroAppSecurityOptions;
}
function resolveSecurityRules(event) {
  if (!event.context.security) {
    event.context.security = {};
  }
  if (!event.context.security.rules) {
    const router = createRouter({ routes: structuredClone(nitroAppSecurityOptions) });
    const matcher = toRouteMatcher(router);
    const matches = matcher.matchAll(event.path.split("?")[0]);
    const rules = defuReplaceArray({}, ...matches.reverse());
    event.context.security.rules = rules;
  }
  return event.context.security.rules;
}
function resolveSecurityRoute(event) {
  if (!event.context.security) {
    event.context.security = {};
  }
  if (!event.context.security.route) {
    const routeNames = Object.fromEntries(Object.entries(nitroAppSecurityOptions).map(([name]) => [name, { name }]));
    const router = createRouter({ routes: routeNames });
    const match = router.lookup(event.path.split("?")[0]);
    const route = match?.name ?? "";
    event.context.security.route = route;
  }
  return event.context.security.route;
}

const KEYS_TO_NAMES = {
  contentSecurityPolicy: "Content-Security-Policy",
  crossOriginEmbedderPolicy: "Cross-Origin-Embedder-Policy",
  crossOriginOpenerPolicy: "Cross-Origin-Opener-Policy",
  crossOriginResourcePolicy: "Cross-Origin-Resource-Policy",
  originAgentCluster: "Origin-Agent-Cluster",
  referrerPolicy: "Referrer-Policy",
  strictTransportSecurity: "Strict-Transport-Security",
  xContentTypeOptions: "X-Content-Type-Options",
  xDNSPrefetchControl: "X-DNS-Prefetch-Control",
  xDownloadOptions: "X-Download-Options",
  xFrameOptions: "X-Frame-Options",
  xPermittedCrossDomainPolicies: "X-Permitted-Cross-Domain-Policies",
  xXSSProtection: "X-XSS-Protection",
  permissionsPolicy: "Permissions-Policy"
};
const NAMES_TO_KEYS = Object.fromEntries(Object.entries(KEYS_TO_NAMES).map(([key, name]) => [name, key]));
function getNameFromKey(key) {
  return KEYS_TO_NAMES[key];
}
function getKeyFromName(headerName) {
  const [, key] = Object.entries(NAMES_TO_KEYS).find(([name]) => name.toLowerCase() === headerName.toLowerCase()) || [];
  return key;
}
function headerStringFromObject(optionKey, optionValue) {
  if (optionValue === false) {
    return "";
  }
  if (optionKey === "contentSecurityPolicy") {
    const policies = optionValue;
    return Object.entries(policies).filter(([, value]) => value !== false).map(([directive, sources]) => {
      if (directive === "upgrade-insecure-requests") {
        return "upgrade-insecure-requests;";
      } else {
        const stringifiedSources = typeof sources === "string" ? sources : sources.map((source) => source.trim()).join(" ");
        return `${directive} ${stringifiedSources};`;
      }
    }).join(" ");
  } else if (optionKey === "strictTransportSecurity") {
    const policies = optionValue;
    return [
      `max-age=${policies.maxAge};`,
      policies.includeSubdomains && "includeSubDomains;",
      policies.preload && "preload;"
    ].filter(Boolean).join(" ");
  } else if (optionKey === "permissionsPolicy") {
    const policies = optionValue;
    return Object.entries(policies).filter(([, value]) => value !== false).map(([directive, sources]) => {
      if (typeof sources === "string") {
        return `${directive}=${sources}`;
      } else {
        return `${directive}=(${sources.join(" ")})`;
      }
    }).join(", ");
  } else {
    return optionValue;
  }
}
function headerObjectFromString(optionKey, headerValue) {
  if (!headerValue) {
    return false;
  }
  if (optionKey === "contentSecurityPolicy") {
    const directives = headerValue.split(";").map((directive) => directive.trim()).filter((directive) => directive);
    const objectForm = {};
    for (const directive of directives) {
      const [type, ...sources] = directive.split(" ").map((token) => token.trim());
      if (type === "upgrade-insecure-requests") {
        objectForm[type] = true;
      } else {
        objectForm[type] = sources.join(" ");
      }
    }
    return objectForm;
  } else if (optionKey === "strictTransportSecurity") {
    const directives = headerValue.split(";").map((directive) => directive.trim()).filter((directive) => directive);
    const objectForm = {};
    for (const directive of directives) {
      const [type, value] = directive.split("=").map((token) => token.trim());
      if (type === "max-age") {
        objectForm.maxAge = Number(value);
      } else if (type === "includeSubdomains" || type === "preload") {
        objectForm[type] = true;
      }
    }
    return objectForm;
  } else if (optionKey === "permissionsPolicy") {
    const directives = headerValue.split(",").map((directive) => directive.trim()).filter((directive) => directive);
    const objectForm = {};
    for (const directive of directives) {
      const [type, value] = directive.split("=").map((token) => token.trim());
      objectForm[type] = value;
    }
    return objectForm;
  } else {
    return headerValue;
  }
}
function standardToSecurity(standardHeaders) {
  if (!standardHeaders) {
    return void 0;
  }
  const standardHeadersAsObject = {};
  Object.entries(standardHeaders).forEach(([headerName, headerValue]) => {
    const optionKey = getKeyFromName(headerName);
    if (optionKey) {
      if (typeof headerValue === "string") {
        const objectValue = headerObjectFromString(optionKey, headerValue);
        standardHeadersAsObject[optionKey] = objectValue;
      } else {
        standardHeadersAsObject[optionKey] = headerValue;
      }
    }
  });
  if (Object.keys(standardHeadersAsObject).length === 0) {
    return void 0;
  }
  return standardHeadersAsObject;
}
function backwardsCompatibleSecurity(securityHeaders) {
  if (!securityHeaders) {
    return void 0;
  }
  const securityHeadersAsObject = {};
  Object.entries(securityHeaders).forEach(([key, value]) => {
    const optionKey = key;
    if ((optionKey === "contentSecurityPolicy" || optionKey === "permissionsPolicy" || optionKey === "strictTransportSecurity") && typeof value === "string") {
      const objectValue = headerObjectFromString(optionKey, value);
      securityHeadersAsObject[optionKey] = objectValue;
    } else if (value === "") {
      securityHeadersAsObject[optionKey] = false;
    } else {
      securityHeadersAsObject[optionKey] = value;
    }
  });
  return securityHeadersAsObject;
}

const _cYiXgN0SVh2rAeMOm1bDlcilAWnoNpxsQ0G1eUHvd9E = defineNitroPlugin(async (nitroApp) => {
  const appSecurityOptions = getAppSecurityOptions();
  const runtimeConfig = useRuntimeConfig();
  for (const route in runtimeConfig.nitro.routeRules) {
    const rule = runtimeConfig.nitro.routeRules[route];
    const { headers: headers2 } = rule;
    const securityHeaders2 = standardToSecurity(headers2);
    if (securityHeaders2) {
      appSecurityOptions[route] = { headers: securityHeaders2 };
    }
  }
  const securityOptions = runtimeConfig.security;
  const { headers } = securityOptions;
  const securityHeaders = backwardsCompatibleSecurity(headers);
  appSecurityOptions["/**"] = defuReplaceArray(
    { headers: securityHeaders },
    securityOptions,
    appSecurityOptions["/**"]
  );
  for (const route in runtimeConfig.nitro.routeRules) {
    const rule = runtimeConfig.nitro.routeRules[route];
    const { security } = rule;
    if (security) {
      const { headers: headers2 } = security;
      const securityHeaders2 = backwardsCompatibleSecurity(headers2);
      appSecurityOptions[route] = defuReplaceArray(
        { headers: securityHeaders2 },
        security,
        appSecurityOptions[route]
      );
    }
  }
  nitroApp.hooks.hook("nuxt-security:headers", ({ route, headers: headers2 }) => {
    appSecurityOptions[route] = defuReplaceArray(
      { headers: headers2 },
      appSecurityOptions[route]
    );
  });
  nitroApp.hooks.hook("nuxt-security:ready", async () => {
    await nitroApp.hooks.callHook("nuxt-security:routeRules", appSecurityOptions);
  });
  await nitroApp.hooks.callHook("nuxt-security:ready");
});

const sriHashes = {"/_nuxt/builds/meta/dev.json":"sha384-oFV6H9bPV7nTcnC8x3C7L9Eioz/VNAtpw9QDhfgWYn9JjfXMh8DsD3zVu1T9toaX","/_nuxt/builds/meta/f2300457-a1b0-4ee5-b6f6-e32346238b5b.json":"sha384-0qI7x4Pjqjj0AeE8dIhQUTfElUp/2IMx8Vl8X/cREdyAfHNwpbKCxtpUHFbt5p8w","/_nuxt/builds/latest.json":"sha384-hDIqhwDMuunDOPFAG8AZO1QwulE6J+G1VKxQtj+a5XyFPY3wzHuzUkGHR8J2lAfv","/ApplySquadLogo.jpeg":"sha384-s5nY8jq15EdPatiAyipXxteU/cSLqe/wZDG5p55I004e4rzGwsIduA1HL5f5X2Fv","/favicon.ico":"sha384-wftgi4pDYMhx/BrOTzSEmlczHpdFmuKJvk3zpYrtmBbiJtw5IsAlLCkmo+c5/k4S","/robots.txt":"sha384-7GZOiJ7WwbJ2PKz3iZ2Vt/NHNz65guUjQZ/uo6o2LYkbO/Al8pImelhUBJCReJw+"};

const SCRIPT_RE$1 = /<script((?=[^>]+\bsrc="([^"]+)")(?![^>]+\bintegrity="[^"]+")[^>]+)(?:\/>|><\/script>)/g;
const LINK_RE$1 = /<link((?=[^>]+\brel="(?:stylesheet|preload|modulepreload)")(?=[^>]+\bhref="([^"]+)")(?![^>]+\bintegrity="[\w\-+/=]+")[^>]+)>/g;
const _gDcq5U5_riGDRwVXZLLA5DV60maf1bR6oQmW_0chaQ = defineNitroPlugin((nitroApp) => {
  nitroApp.hooks.hook("render:html", (html, { event }) => {
    const rules = resolveSecurityRules(event);
    if (!rules.enabled || !rules.sri) {
      return;
    }
    const sections = ["body", "bodyAppend", "bodyPrepend", "head"];
    for (const section of sections) {
      html[section] = html[section].map((element) => {
        if (typeof element !== "string") {
          return element;
        }
        element = element.replace(SCRIPT_RE$1, (match, rest, src) => {
          const hash = sriHashes[src];
          if (hash) {
            const integrityScript = `<script integrity="${hash}"${rest}><\/script>`;
            return integrityScript;
          } else {
            return match;
          }
        });
        element = element.replace(LINK_RE$1, (match, rest, href) => {
          const hash = sriHashes[href];
          if (hash) {
            const integrityLink = `<link integrity="${hash}"${rest}>`;
            return integrityLink;
          } else {
            return match;
          }
        });
        return element;
      });
    }
  });
});

globalThis.crypto ??= webcrypto;
function generateRandomNonce() {
  const array = new Uint8Array(18);
  crypto.getRandomValues(array);
  const nonce = btoa(String.fromCharCode(...array));
  return nonce;
}

const _xumVUneIVopsIsx3d3q0FnxAcCVUa82mWxFrEwaftc = defineNitroPlugin((nitroApp) => {
  {
    return;
  }
});

const LINK_RE = /<link([^>]*?>)/gi;
const NONCE_RE = /nonce="[^"]+"/i;
const SCRIPT_RE = /<script([^>]*?>)/gi;
const STYLE_RE = /<style([^>]*?>)/gi;
const _arENEgInD4Rd0_p8YzMnAeWxEHsPrrSKNOkXQcIjc = defineNitroPlugin((nitroApp) => {
  nitroApp.hooks.hook("request", (event) => {
    if (event.context.security?.nonce) {
      return;
    }
    const rules = resolveSecurityRules(event);
    if (rules.enabled && rules.nonce && true) {
      const nonce = generateRandomNonce();
      event.context.security.nonce = nonce;
    }
  });
  nitroApp.hooks.hook("render:html", (html, { event }) => {
    const rules = resolveSecurityRules(event);
    if (!rules.enabled || !rules.headers || !rules.headers.contentSecurityPolicy || !rules.nonce) {
      return;
    }
    const nonce = event.context.security.nonce;
    const sections = ["body", "bodyAppend", "bodyPrepend", "head"];
    for (const section of sections) {
      html[section] = html[section].map((element) => {
        if (typeof element !== "string") {
          return element;
        }
        element = element.replace(LINK_RE, (match, rest) => {
          if (NONCE_RE.test(rest)) {
            return match.replace(NONCE_RE, `nonce="${nonce}"`);
          }
          return `<link nonce="${nonce}"` + rest;
        });
        element = element.replace(SCRIPT_RE, (match, rest) => {
          return `<script nonce="${nonce}"` + rest;
        });
        element = element.replace(STYLE_RE, (match, rest) => {
          return `<style nonce="${nonce}"` + rest;
        });
        return element;
      });
    }
    {
      html.head.push(
        `<meta property="csp-nonce" nonce="${nonce}">`
      );
    }
  });
});

const _YGk1whTWblO46Y82vFpiZS3Eyg4kdLtImhb4eMjCUs = defineNitroPlugin((nitroApp) => {
  nitroApp.hooks.hook("render:html", (response, { event }) => {
    if (response.island) {
      return;
    }
    const rules = resolveSecurityRules(event);
    if (rules.enabled && rules.headers) {
      const headers = rules.headers;
      if (headers.contentSecurityPolicy) {
        const csp = headers.contentSecurityPolicy;
        const nonce = event.context.security?.nonce;
        const scriptHashes = event.context.security?.hashes?.script;
        const styleHashes = event.context.security?.hashes?.style;
        headers.contentSecurityPolicy = updateCspVariables(csp, nonce, scriptHashes, styleHashes);
      }
    }
  });
});
function updateCspVariables(csp, nonce, scriptHashes, styleHashes) {
  const generatedCsp = Object.fromEntries(Object.entries(csp).map(([directive, value]) => {
    if (typeof value === "boolean") {
      return [directive, value];
    }
    const sources = typeof value === "string" ? value.split(" ").map((token) => token.trim()).filter((token) => token) : value;
    const modifiedSources = sources.filter((source) => {
      if (source.startsWith("'nonce-") && source !== "'nonce-{{nonce}}'") {
        console.warn("[nuxt-security] removing static nonce from CSP header");
        return false;
      }
      return true;
    }).map((source) => {
      if (source === "'nonce-{{nonce}}'") {
        return nonce ? `'nonce-${nonce}'` : "";
      } else {
        return source;
      }
    }).filter((source) => source);
    if (directive === "script-src" && scriptHashes) {
      modifiedSources.push(...scriptHashes);
    }
    if (directive === "style-src" && styleHashes) {
      modifiedSources.push(...styleHashes);
    }
    return [directive, modifiedSources];
  }));
  return generatedCsp;
}

const _zPFerxhCqru219M5Vhj5hsmEyxGImm8HBuKLuKBVU4 = defineNitroPlugin((nitroApp) => {
  {
    return;
  }
});

const _YYoB_oP2KwplSPLRutzIKzcP0kesUqxx8snlQpGYuk = defineNitroPlugin((nitroApp) => {
  nitroApp.hooks.hook("render:response", (response, { event }) => {
    const rules = resolveSecurityRules(event);
    if (rules.enabled && rules.headers) {
      const headers = rules.headers;
      Object.entries(headers).forEach(([header, value]) => {
        const headerName = getNameFromKey(header);
        if (value === false) {
          const { headers: standardHeaders } = getRouteRules(event);
          const standardHeaderValue = standardHeaders?.[headerName];
          const currentHeaderValue = getResponseHeader(event, headerName);
          if (standardHeaderValue === currentHeaderValue) {
            removeResponseHeader(event, headerName);
          }
        } else {
          const headerValue = headerStringFromObject(header, value);
          setResponseHeader(event, headerName, headerValue);
        }
      });
    }
  });
});

const _NgGDGWGnFPD88N8MPikhkLTsswIV3EYEptMcNiDnago = defineNitroPlugin((nitroApp) => {
  nitroApp.hooks.hook("beforeResponse", (event) => {
    const rules = resolveSecurityRules(event);
    if (rules.enabled && rules.hidePoweredBy && !event.node.res.headersSent) {
      removeResponseHeader(event, "x-powered-by");
    }
  });
});

const _Vp7PO6Y0GIjzTjz8tw0RczqZmtGBtP5hjZfXOi5BY = defineNitroPlugin(async (nitroApp) => {
  {
    const prerenderedHeaders = await useStorage("assets:nuxt-security").getItem("headers.json") || {};
    nitroApp.hooks.hook("beforeResponse", (event) => {
      const rules = resolveSecurityRules(event);
      if (rules.enabled && rules.ssg && rules.ssg.nitroHeaders) {
        const path = event.path.split("?")[0];
        if (prerenderedHeaders[path]) {
          setResponseHeaders(event, prerenderedHeaders[path]);
        }
      }
    });
  }
});

const plugins = [
  _5d6SKuf4ChrJuBLa1CGU5qeV1hBVVPFJzLGpGTUfxc4,
_xmO_yjnWQmfJEHn8xg7CV2D9ejRzj2WzvhUIuUBxQJs,
_ps4b82qqg1u2yyEUlr_JnACLJr_CPL4jS219btWWKrY,
_cYiXgN0SVh2rAeMOm1bDlcilAWnoNpxsQ0G1eUHvd9E,
_gDcq5U5_riGDRwVXZLLA5DV60maf1bR6oQmW_0chaQ,
_xumVUneIVopsIsx3d3q0FnxAcCVUa82mWxFrEwaftc,
_arENEgInD4Rd0_p8YzMnAeWxEHsPrrSKNOkXQcIjc,
_YGk1whTWblO46Y82vFpiZS3Eyg4kdLtImhb4eMjCUs,
_zPFerxhCqru219M5Vhj5hsmEyxGImm8HBuKLuKBVU4,
_YYoB_oP2KwplSPLRutzIKzcP0kesUqxx8snlQpGYuk,
_NgGDGWGnFPD88N8MPikhkLTsswIV3EYEptMcNiDnago,
_Vp7PO6Y0GIjzTjz8tw0RczqZmtGBtP5hjZfXOi5BY
];

const warnOnceSet = /* @__PURE__ */ new Set();
const DEFAULT_ENDPOINT = "https://api.iconify.design";
const _aYa4n9 = defineCachedEventHandler(async (event) => {
  const url = getRequestURL(event);
  if (!url)
    return createError({ status: 400, message: "Invalid icon request" });
  const options = useAppConfig().icon;
  const collectionName = event.context.params?.collection?.replace(/\.json$/, "");
  const collection = collectionName ? await collections[collectionName]?.() : null;
  const apiEndPoint = options.iconifyApiEndpoint || DEFAULT_ENDPOINT;
  const icons = url.searchParams.get("icons")?.split(",");
  if (collection) {
    if (icons?.length) {
      const data = getIcons(
        collection,
        icons
      );
      consola$1.debug(`[Icon] serving ${(icons || []).map((i) => "`" + collectionName + ":" + i + "`").join(",")} from bundled collection`);
      return data;
    }
  } else {
    if (collectionName && !warnOnceSet.has(collectionName) && apiEndPoint === DEFAULT_ENDPOINT) {
      consola$1.warn([
        `[Icon] Collection \`${collectionName}\` is not found locally`,
        `We suggest to install it via \`npm i -D @iconify-json/${collectionName}\` to provide the best end-user experience.`
      ].join("\n"));
      warnOnceSet.add(collectionName);
    }
  }
  if (options.fallbackToApi === true || options.fallbackToApi === "server-only") {
    const apiUrl = new URL("./" + basename(url.pathname) + url.search, apiEndPoint);
    consola$1.debug(`[Icon] fetching ${(icons || []).map((i) => "`" + collectionName + ":" + i + "`").join(",")} from iconify api`);
    if (apiUrl.host !== new URL(apiEndPoint).host) {
      return createError({ status: 400, message: "Invalid icon request" });
    }
    try {
      const data = await $fetch(apiUrl.href);
      return data;
    } catch (e) {
      consola$1.error(e);
      if (e.status === 404)
        return createError({ status: 404 });
      else
        return createError({ status: 500, message: "Failed to fetch fallback icon" });
    }
  }
  return createError({ status: 404 });
}, {
  group: "nuxt",
  name: "icon",
  getKey(event) {
    const collection = event.context.params?.collection?.replace(/\.json$/, "") || "unknown";
    const icons = String(getQuery$1(event).icons || "");
    return `${collection}_${icons.split(",")[0]}_${icons.length}_${hash$1(icons)}`;
  },
  swr: true,
  maxAge: 60 * 60 * 24 * 7
  // 1 week
});

const defaultThrowErrorValue = { throwError: true };
const defaultSecurityConfig = (serverlUrl, strict) => {
  const defaultConfig = {
    strict,
    headers: {
      crossOriginResourcePolicy: "same-origin",
      crossOriginOpenerPolicy: "same-origin",
      crossOriginEmbedderPolicy: "unsafe-none" ,
      contentSecurityPolicy: {
        "base-uri": ["'none'"],
        "font-src": ["'self'", "https:", "data:"],
        "form-action": ["'self'"],
        "frame-ancestors": ["'self'"],
        "img-src": ["'self'", "data:"],
        "object-src": ["'none'"],
        "script-src-attr": ["'none'"],
        "style-src": ["'self'", "https:", "'unsafe-inline'"],
        "script-src": ["'self'", "https:", "'unsafe-inline'", "'strict-dynamic'", "'nonce-{{nonce}}'"],
        "upgrade-insecure-requests": true
      },
      originAgentCluster: "?1",
      referrerPolicy: "no-referrer",
      strictTransportSecurity: {
        maxAge: 15552e3,
        includeSubdomains: true
      },
      xContentTypeOptions: "nosniff",
      xDNSPrefetchControl: "off",
      xDownloadOptions: "noopen",
      xFrameOptions: "SAMEORIGIN",
      xPermittedCrossDomainPolicies: "none",
      xXSSProtection: "0",
      permissionsPolicy: {
        camera: [],
        "display-capture": [],
        fullscreen: [],
        geolocation: [],
        microphone: []
      }
    },
    requestSizeLimiter: {
      maxRequestSizeInBytes: 2e6,
      maxUploadFileRequestInBytes: 8e6,
      ...defaultThrowErrorValue
    },
    rateLimiter: {
      // Twitter search rate limiting
      tokensPerInterval: 150,
      interval: 3e5,
      headers: false,
      driver: {
        name: "lruCache"
      },
      whiteList: void 0,
      ...defaultThrowErrorValue
    },
    xssValidator: {
      methods: ["GET", "POST"],
      ...defaultThrowErrorValue
    },
    corsHandler: {
      // Options by CORS middleware for Express https://github.com/expressjs/cors#configuration-options
      origin: serverlUrl,
      methods: ["GET", "HEAD", "PUT", "PATCH", "POST", "DELETE"],
      preflight: {
        statusCode: 204
      }
    },
    allowedMethodsRestricter: {
      methods: "*",
      ...defaultThrowErrorValue
    },
    hidePoweredBy: true,
    basicAuth: false,
    enabled: true,
    csrf: false,
    nonce: true,
    removeLoggers: true,
    ssg: {
      meta: true,
      hashScripts: true,
      hashStyles: false,
      nitroHeaders: true,
      exportToPresets: true
    },
    sri: true
  };
  return defaultConfig;
};

const FILE_UPLOAD_HEADER = "multipart/form-data";
const defaultSizeLimiter = defaultSecurityConfig("").requestSizeLimiter;
const _d0SzZ2 = defineEventHandler((event) => {
  const rules = resolveSecurityRules(event);
  if (rules.enabled && rules.requestSizeLimiter) {
    const requestSizeLimiter = defu(
      rules.requestSizeLimiter,
      defaultSizeLimiter
    );
    if (["POST", "PUT", "DELETE"].includes(event.node.req.method)) {
      const contentLengthValue = getRequestHeader(event, "content-length");
      const contentTypeValue = getRequestHeader(event, "content-type");
      const isFileUpload = contentTypeValue?.includes(FILE_UPLOAD_HEADER);
      const requestLimit = isFileUpload ? requestSizeLimiter.maxUploadFileRequestInBytes : requestSizeLimiter.maxRequestSizeInBytes;
      if (parseInt(contentLengthValue) >= requestLimit) {
        const payloadTooLargeError = {
          statusCode: 413,
          statusMessage: "Payload Too Large"
        };
        if (requestSizeLimiter.throwError === false) {
          return payloadTooLargeError;
        }
        throw createError(payloadTooLargeError);
      }
    }
  }
});

const _bfpOK5 = defineEventHandler((event) => {
  const rules = resolveSecurityRules(event);
  if (rules.enabled && rules.corsHandler) {
    const { corsHandler } = rules;
    let origin;
    if (typeof corsHandler.origin === "string" && corsHandler.origin !== "*") {
      origin = [corsHandler.origin];
    } else {
      origin = corsHandler.origin;
    }
    if (origin && origin !== "*" && corsHandler.useRegExp) {
      origin = origin.map((o) => new RegExp(o, "i"));
    }
    handleCors(event, {
      origin,
      methods: corsHandler.methods,
      allowHeaders: corsHandler.allowHeaders,
      exposeHeaders: corsHandler.exposeHeaders,
      credentials: corsHandler.credentials,
      maxAge: corsHandler.maxAge,
      preflight: corsHandler.preflight
    });
  }
});

const _MWBiaw = defineEventHandler((event) => {
  const rules = resolveSecurityRules(event);
  if (rules.enabled && rules.allowedMethodsRestricter) {
    const { allowedMethodsRestricter } = rules;
    const allowedMethods = allowedMethodsRestricter.methods;
    if (allowedMethods !== "*" && !allowedMethods.includes(event.node.req.method)) {
      const methodNotAllowedError = {
        statusCode: 405,
        statusMessage: "Method not allowed"
      };
      if (allowedMethodsRestricter.throwError === false) {
        return methodNotAllowedError;
      }
      throw createError(methodNotAllowedError);
    }
  }
});

const storage = useStorage("#rate-limiter-storage");
const defaultRateLimiter = defaultSecurityConfig("").rateLimiter;
const _gY4jkI = defineEventHandler(async (event) => {
  const rules = resolveSecurityRules(event);
  const route = resolveSecurityRoute(event);
  if (rules.enabled && rules.rateLimiter) {
    const rateLimiter = defu(
      rules.rateLimiter,
      defaultRateLimiter
    );
    const ip = getIP(event);
    if (rateLimiter.whiteList && rateLimiter.whiteList.includes(ip)) {
      return;
    }
    const url = ip + route;
    let storageItem = await storage.getItem(url);
    if (!storageItem) {
      await setStorageItem(rateLimiter, url);
    } else {
      if (typeof storageItem !== "object") {
        return;
      }
      const timeSinceFirstRateLimit = storageItem.date;
      const timeForInterval = storageItem.date + Number(rateLimiter.interval);
      if (Date.now() >= timeForInterval) {
        await setStorageItem(rateLimiter, url);
        storageItem = await storage.getItem(url);
      }
      const isLimited = timeSinceFirstRateLimit <= timeForInterval && storageItem.value === 0;
      if (isLimited) {
        const tooManyRequestsError = {
          statusCode: 429,
          statusMessage: "Too Many Requests"
        };
        if (rules.rateLimiter.headers) {
          setResponseHeader(event, "x-ratelimit-remaining", 0);
          setResponseHeader(event, "x-ratelimit-limit", rateLimiter.tokensPerInterval);
          setResponseHeader(event, "x-ratelimit-reset", timeForInterval);
        }
        if (rateLimiter.throwError === false) {
          return tooManyRequestsError;
        }
        throw createError(tooManyRequestsError);
      }
      const newItemDate = timeSinceFirstRateLimit > timeForInterval ? Date.now() : storageItem.date;
      const newStorageItem = { value: storageItem.value - 1, date: newItemDate };
      await storage.setItem(url, newStorageItem);
      const currentItem = await storage.getItem(url);
      if (currentItem && rateLimiter.headers) {
        setResponseHeader(event, "x-ratelimit-remaining", currentItem.value);
        setResponseHeader(event, "x-ratelimit-limit", rateLimiter.tokensPerInterval);
        setResponseHeader(event, "x-ratelimit-reset", timeForInterval);
      }
    }
  }
});
async function setStorageItem(rateLimiter, url) {
  const rateLimitedObject = { value: rateLimiter.tokensPerInterval, date: Date.now() };
  await storage.setItem(url, rateLimitedObject);
}
function getIP(event) {
  const ip = getRequestIP(event, { xForwardedFor: true }) || "";
  return ip;
}

const _9Fo5ql = defineEventHandler(async (event) => {
  const rules = resolveSecurityRules(event);
  if (rules.enabled && rules.xssValidator) {
    const filterOpt = {
      ...rules.xssValidator,
      escapeHtml: void 0
    };
    if (rules.xssValidator.escapeHtml === false) {
      filterOpt.escapeHtml = (value) => value;
    }
    const xssValidator = new FilterXSS(filterOpt);
    if (event.node.req.socket.readyState !== "readOnly") {
      if (rules.xssValidator.methods && rules.xssValidator.methods.includes(
        event.node.req.method
      )) {
        const valueToFilter = event.node.req.method === "GET" ? getQuery$1(event) : event.node.req.headers["content-type"]?.includes(
          "multipart/form-data"
        ) ? await readMultipartFormData(event) : await readBody(event);
        if (valueToFilter && Object.keys(valueToFilter).length) {
          if (valueToFilter.statusMessage && valueToFilter.statusMessage !== "Bad Request") {
            return;
          }
          const stringifiedValue = JSON.stringify(valueToFilter);
          const processedValue = xssValidator.process(
            JSON.stringify(valueToFilter)
          );
          if (processedValue !== stringifiedValue) {
            const badRequestError = {
              statusCode: 400,
              statusMessage: "Bad Request"
            };
            if (rules.xssValidator.throwError === false) {
              return badRequestError;
            }
            throw createError(badRequestError);
          }
        }
      }
    }
  }
});

const _lazy_IfUftA = () => Promise.resolve().then(function () { return regenerateCoverLetter$1; });
const _lazy_rHiuA1 = () => Promise.resolve().then(function () { return completeEmailSetup$1; });
const _lazy_wX3TyV = () => Promise.resolve().then(function () { return initEmailSetup$1; });
const _lazy_xF46Gx = () => Promise.resolve().then(function () { return revokeEmailAccess$1; });
const _lazy_eK9PXv = () => Promise.resolve().then(function () { return renderer$1; });

const handlers = [
  { route: '/api/regenerate-cover-letter', handler: _lazy_IfUftA, lazy: true, middleware: false, method: undefined },
  { route: '/complete-email-setup', handler: _lazy_rHiuA1, lazy: true, middleware: false, method: undefined },
  { route: '/init-email-setup', handler: _lazy_wX3TyV, lazy: true, middleware: false, method: undefined },
  { route: '/revoke-email-access', handler: _lazy_xF46Gx, lazy: true, middleware: false, method: undefined },
  { route: '/__nuxt_error', handler: _lazy_eK9PXv, lazy: true, middleware: false, method: undefined },
  { route: '/api/_nuxt_icon/:collection', handler: _aYa4n9, lazy: false, middleware: false, method: undefined },
  { route: '', handler: _d0SzZ2, lazy: false, middleware: false, method: undefined },
  { route: '', handler: _bfpOK5, lazy: false, middleware: false, method: undefined },
  { route: '', handler: _MWBiaw, lazy: false, middleware: false, method: undefined },
  { route: '', handler: _gY4jkI, lazy: false, middleware: false, method: undefined },
  { route: '', handler: _9Fo5ql, lazy: false, middleware: false, method: undefined },
  { route: '/**', handler: _lazy_eK9PXv, lazy: true, middleware: false, method: undefined }
];

function _captureError(error, type) {
  console.error(`[${type}]`, error);
  useNitroApp().captureError(error, { tags: [type] });
}
function trapUnhandledNodeErrors() {
  process.on(
    "unhandledRejection",
    (error) => _captureError(error, "unhandledRejection")
  );
  process.on(
    "uncaughtException",
    (error) => _captureError(error, "uncaughtException")
  );
}
function joinHeaders(value) {
  return Array.isArray(value) ? value.join(", ") : String(value);
}
function normalizeFetchResponse(response) {
  if (!response.headers.has("set-cookie")) {
    return response;
  }
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: normalizeCookieHeaders(response.headers)
  });
}
function normalizeCookieHeader(header = "") {
  return splitCookiesString(joinHeaders(header));
}
function normalizeCookieHeaders(headers) {
  const outgoingHeaders = new Headers();
  for (const [name, header] of headers) {
    if (name === "set-cookie") {
      for (const cookie of normalizeCookieHeader(header)) {
        outgoingHeaders.append("set-cookie", cookie);
      }
    } else {
      outgoingHeaders.set(name, joinHeaders(header));
    }
  }
  return outgoingHeaders;
}

function createNitroApp() {
  const config = useRuntimeConfig();
  const hooks = createHooks();
  const captureError = (error, context = {}) => {
    const promise = hooks.callHookParallel("error", error, context).catch((error_) => {
      console.error("Error while capturing another error", error_);
    });
    if (context.event && isEvent(context.event)) {
      const errors = context.event.context.nitro?.errors;
      if (errors) {
        errors.push({ error, context });
      }
      if (context.event.waitUntil) {
        context.event.waitUntil(promise);
      }
    }
  };
  const h3App = createApp({
    debug: destr(true),
    onError: (error, event) => {
      captureError(error, { event, tags: ["request"] });
      return errorHandler(error, event);
    },
    onRequest: async (event) => {
      event.context.nitro = event.context.nitro || { errors: [] };
      const envContext = event.node.req?.__unenv__;
      if (envContext) {
        Object.assign(event.context, envContext);
      }
      event.fetch = (req, init) => fetchWithEvent(event, req, init, { fetch: localFetch });
      event.$fetch = (req, init) => fetchWithEvent(event, req, init, {
        fetch: $fetch
      });
      event.waitUntil = (promise) => {
        if (!event.context.nitro._waitUntilPromises) {
          event.context.nitro._waitUntilPromises = [];
        }
        event.context.nitro._waitUntilPromises.push(promise);
        if (envContext?.waitUntil) {
          envContext.waitUntil(promise);
        }
      };
      event.captureError = (error, context) => {
        captureError(error, { event, ...context });
      };
      await nitroApp$1.hooks.callHook("request", event).catch((error) => {
        captureError(error, { event, tags: ["request"] });
      });
    },
    onBeforeResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("beforeResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    },
    onAfterResponse: async (event, response) => {
      await nitroApp$1.hooks.callHook("afterResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    }
  });
  const router = createRouter$1({
    preemptive: true
  });
  const nodeHandler = toNodeListener(h3App);
  const localCall = (aRequest) => callNodeRequestHandler(nodeHandler, aRequest);
  const localFetch = (input, init) => {
    if (!input.toString().startsWith("/")) {
      return globalThis.fetch(input, init);
    }
    return fetchNodeRequestHandler(
      nodeHandler,
      input,
      init
    ).then((response) => normalizeFetchResponse(response));
  };
  const $fetch = createFetch({
    fetch: localFetch,
    Headers: Headers$1,
    defaults: { baseURL: config.app.baseURL }
  });
  globalThis.$fetch = $fetch;
  h3App.use(createRouteRulesHandler({ localFetch }));
  for (const h of handlers) {
    let handler = h.lazy ? lazyEventHandler(h.handler) : h.handler;
    if (h.middleware || !h.route) {
      const middlewareBase = (config.app.baseURL + (h.route || "/")).replace(
        /\/+/g,
        "/"
      );
      h3App.use(middlewareBase, handler);
    } else {
      const routeRules = getRouteRulesForPath(
        h.route.replace(/:\w+|\*\*/g, "_")
      );
      if (routeRules.cache) {
        handler = cachedEventHandler(handler, {
          group: "nitro/routes",
          ...routeRules.cache
        });
      }
      router.use(h.route, handler, h.method);
    }
  }
  h3App.use(config.app.baseURL, router.handler);
  const app = {
    hooks,
    h3App,
    router,
    localCall,
    localFetch,
    captureError
  };
  return app;
}
function runNitroPlugins(nitroApp2) {
  for (const plugin of plugins) {
    try {
      plugin(nitroApp2);
    } catch (error) {
      nitroApp2.captureError(error, { tags: ["plugin"] });
      throw error;
    }
  }
}
const nitroApp$1 = createNitroApp();
function useNitroApp() {
  return nitroApp$1;
}
runNitroPlugins(nitroApp$1);

if (!globalThis.crypto) {
  globalThis.crypto = nodeCrypto;
}
const {
  NITRO_NO_UNIX_SOCKET,
  NITRO_DEV_WORKER_DIR = ".",
  NITRO_DEV_WORKER_ID
} = process.env;
trapUnhandledNodeErrors();
parentPort?.on("message", (msg) => {
  if (msg && msg.event === "shutdown") {
    shutdown();
  }
});
const nitroApp = useNitroApp();
const server = new Server(toNodeListener(nitroApp.h3App));
let listener;
listen().catch(() => listen(
  true
  /* use random port */
)).catch((error) => {
  console.error("Dev worker failed to listen:", error);
  return shutdown();
});
nitroApp.router.get(
  "/_nitro/tasks",
  defineEventHandler(async (event) => {
    const _tasks = await Promise.all(
      Object.entries(tasks).map(async ([name, task]) => {
        const _task = await task.resolve?.();
        return [name, { description: _task?.meta?.description }];
      })
    );
    return {
      tasks: Object.fromEntries(_tasks),
      scheduledTasks
    };
  })
);
nitroApp.router.use(
  "/_nitro/tasks/:name",
  defineEventHandler(async (event) => {
    const name = getRouterParam(event, "name");
    const payload = {
      ...getQuery$1(event),
      ...await readBody(event).then((r) => r?.payload).catch(() => ({}))
    };
    return await runTask(name, { payload });
  })
);
function listen(useRandomPort = Boolean(
  NITRO_NO_UNIX_SOCKET || process.versions.webcontainer
)) {
  return new Promise((resolve, reject) => {
    try {
      listener = server.listen(useRandomPort ? 0 : getSocketAddress(), () => {
        const address = server.address();
        parentPort?.postMessage({
          event: "listen",
          address: typeof address === "string" ? { socketPath: address } : { host: "localhost", port: address?.port }
        });
        resolve();
      });
    } catch (error) {
      reject(error);
    }
  });
}
function getSocketAddress() {
  const socketName = `worker-${process.pid}-${threadId}-${Math.round(Math.random() * 1e4)}-${NITRO_DEV_WORKER_ID}.sock`;
  const socketPath = join(NITRO_DEV_WORKER_DIR, socketName);
  if (process.platform === "win32") {
    return join(String.raw`\\.\pipe\nitro`, socketPath);
  }
  if (process.platform === "linux" && !T) {
    return `\0${socketPath}`;
  }
  return socketPath;
}
async function shutdown() {
  server.closeAllConnections?.();
  await Promise.all([
    new Promise((resolve) => listener?.close(resolve)),
    nitroApp.hooks.callHook("close").catch(console.error)
  ]);
  parentPort?.postMessage({ event: "exit" });
}

const _messages = { "appName": "Nuxt", "version": "", "statusCode": 500, "statusMessage": "Server error", "description": "An error occurred in the application and the page could not be served. If you are the application owner, check your server logs for details.", "stack": "" };
const template$1 = (messages) => {
  messages = { ..._messages, ...messages };
  return '<!DOCTYPE html><html lang="en"><head><title>' + messages.statusCode + " - " + messages.statusMessage + " | " + messages.appName + `</title><meta charset="utf-8"><meta content="width=device-width,initial-scale=1.0,minimum-scale=1.0" name="viewport"><style>.spotlight{background:linear-gradient(45deg,#00dc82,#36e4da 50%,#0047e1);bottom:-40vh;filter:blur(30vh);height:60vh;opacity:.8}*,:after,:before{border-color:var(--un-default-border-color,#e5e7eb);border-style:solid;border-width:0;box-sizing:border-box}:after,:before{--un-content:""}html{line-height:1.5;-webkit-text-size-adjust:100%;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-moz-tab-size:4;tab-size:4;-webkit-tap-highlight-color:transparent}body{line-height:inherit;margin:0}h1{font-size:inherit;font-weight:inherit}pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-size:1em;font-variation-settings:normal}h1,p,pre{margin:0}*,:after,:before{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 transparent;--un-ring-shadow:0 0 transparent;--un-shadow-inset: ;--un-shadow:0 0 transparent;--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgba(147,197,253,.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: }.fixed{position:fixed}.left-0{left:0}.right-0{right:0}.z-10{z-index:10}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.h-auto{height:auto}.min-h-screen{min-height:100vh}.flex{display:flex}.flex-1{flex:1 1 0%}.flex-col{flex-direction:column}.overflow-y-auto{overflow-y:auto}.rounded-t-md{border-top-left-radius:.375rem;border-top-right-radius:.375rem}.bg-black\\/5{background-color:#0000000d}.bg-white{--un-bg-opacity:1;background-color:rgb(255 255 255/var(--un-bg-opacity))}.p-8{padding:2rem}.px-10{padding-left:2.5rem;padding-right:2.5rem}.pt-14{padding-top:3.5rem}.text-6xl{font-size:3.75rem;line-height:1}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-black{--un-text-opacity:1;color:rgb(0 0 0/var(--un-text-opacity))}.font-light{font-weight:300}.font-medium{font-weight:500}.leading-tight{line-height:1.25}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}@media (prefers-color-scheme:dark){.dark\\:bg-black{--un-bg-opacity:1;background-color:rgb(0 0 0/var(--un-bg-opacity))}.dark\\:bg-white\\/10{background-color:#ffffff1a}.dark\\:text-white{--un-text-opacity:1;color:rgb(255 255 255/var(--un-text-opacity))}}@media (min-width:640px){.sm\\:text-2xl{font-size:1.5rem;line-height:2rem}.sm\\:text-8xl{font-size:6rem;line-height:1}}</style><script>!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))r(e);new MutationObserver((e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&r(e)})).observe(document,{childList:!0,subtree:!0})}function r(e){if(e.ep)return;e.ep=!0;const r=function(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?r.credentials="include":"anonymous"===e.crossOrigin?r.credentials="omit":r.credentials="same-origin",r}(e);fetch(e.href,r)}}();<\/script></head><body class="antialiased bg-white dark:bg-black dark:text-white flex flex-col font-sans min-h-screen pt-14 px-10 text-black"><div class="fixed left-0 right-0 spotlight"></div><h1 class="font-medium mb-6 sm:text-8xl text-6xl">` + messages.statusCode + '</h1><p class="font-light leading-tight mb-8 sm:text-2xl text-xl">' + messages.description + '</p><div class="bg-black/5 bg-white dark:bg-white/10 flex-1 h-auto overflow-y-auto rounded-t-md"><pre class="font-light leading-tight p-8 text-xl z-10">' + messages.stack + "</pre></div></body></html>";
};

const errorDev = /*#__PURE__*/Object.freeze({
  __proto__: null,
  template: template$1
});

const MODELS = {
  GPT4oMINI: openai("gpt-4o-mini"),
  GPT4o: openai("gpt-4o"),
  MISTRAL_SM: mistral("mistral-small-latest"),
  CLAUDE_HAIKU: anthropic("claude-3-5-haiku-20241022")
};

const PROMPTS = {
  cover_letter: {
    v2: `You are an expert cover letter generator. You are the best at writing cover letters that get job seekers hired.
        
        From the one or more resumes attached, select the one that best fits the specified Job Description. 
        
        Use that Resume to generate a cover letter for the Job Description. 

        Write a cover letter in a simple and professional tone, strictly following these guidelines: 
        - The cover letter should be less than 300 words.
        - Strictly avoid these words: [ "excited", "perfectly", "deeply", "particularly", "strongly", "strong", "for instance" ].
        - Write to the company, avoid the hiring manager except in the greeting.
        - Reference only explicit experiences in the Resume.
        - This cover letter should be complete and ready to be sent, with no placeholders.
        - Written in first person and emphasize the match between the skills from the Resume and the Job Description. 
        - Heading should include the name of the company, and a greeting 'Dear Hiring Manager,'
        - First paragraph: why the job seeker is drawn to the role, using their personal values that match the job description.
        - Second paragraph: relate the the job seekers background from the resume to the job description.
        - Third paragraph: the interest of the job seeker to help the company and a thank you for consideration. 
        - The signature should include the name, linkedin profile (if available), the email(s) and the phone(s) if available from the resume.
        - The output should be HTML in minified format,
            with no line breaks,
            with no code block formatting, 
            with only tags [ p, br, a ],
            with no other text or preamble in response.
        `
  }};

async function generateCoverLetter(custInfo, jobInfo) {
  console.log("Generating cover letter");
  const cvText = await Promise.all(custInfo.cvs.map(async (document, index) => ` [BEGIN_RESUME ${index}: ${await toText(await document.arrayBuffer())} :END_RESUME ${index}] `));
  const userPrompt = buildUserPrompt(custInfo, jobInfo);
  const systemPrompt = buildSystemPrompt();
  const aiModel = MODELS.GPT4o;
  console.log(`attaching ${cvText.length} characters from resumes to cover letter request`);
  console.log("user prompt:", userPrompt);
  console.log("system prompt:", systemPrompt);
  console.log("ai model:", aiModel);
  let coverLetter = "";
  try {
    const timer = startTimer();
    const { text } = await generateText({
      model: aiModel,
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: [
            { type: "text", text: userPrompt },
            { type: "text", text: cvText.join(" || ") }
          ]
        }
      ]
    });
    timer.stop();
    console.log(`AI call took [${timer.getTotalTime()}]ms`);
    console.log("generated cover letter", text);
    coverLetter = text;
  } catch (e) {
    console.error("error on generate text", e);
    throw e;
  }
  return coverLetter;
}
function startTimer() {
  const stopwatch = new StopWatch();
  stopwatch.start();
  return stopwatch;
}
async function toText(buffer) {
  const text = await pdfToText(Buffer$1.from(buffer));
  console.log("pdf text:", text);
  return text;
}
const example = "Here is an example cover letter: [ <p>Capco</p><p><br></p><p>Dear Hiring Manager,</p><p><br></p><p>What drew me to Capco is the opportunity to work at the intersection of digital transformation and analytics at a global scale. This role is a strong fit with my experience driving data-led decision-making and strategic consulting, and I am excited by the challenge of applying that to a diverse portfolio of products and markets.</p><p><br></p><p>My professional background at IBM Consulting as a Business Transformation Consultant aligns closely with Capco's requirements. I led a diverse team of 40 software testers in a large-scale system conversion project, optimizing data use and enhancing team efficiency by 30%. My experience includes designing data-driven reports, orchestrating strategies to leverage infrastructure data, and coordinating cross-functional project management, which directly relates to Capco's focus on digital transformation and data analytics.</p><p><br></p><p>I am eager to bring my experience in digital transformation and strategy to Capco. Thank you for your time and consideration. I look forward to the opportunity to contribute to your team.</p><p><br></p><p>Sincerely,</p><p>John Doe</p><p><a href='https://www.linkedin.com/in/john-doe' target='_blank'>https://www.linkedin.com/in/john-doe</a></p><p><EMAIL> / <EMAIL></p><p>******-430-4898</p> ]";
const exampleFormatting = "Use this html formatting: [ <p>{Company}</p><p><br></p><p>Dear Hiring Manager,</p><p><br></p><p>{paragraph 1}</p><p><br></p><p>{paragraph 2}</p><p><br></p><p>{paragraph 3}</p><p><br></p><p>Sincerely,</p><p>{name}</p><p><a href='{linkedin url}' target='_blank'>{linkedin url}</a></p><p>{email}</p><p>{phone}</p> ]. ";
function buildUserPrompt(custInfo, jobInfo) {
  let prompt = "";
  if (custInfo.functionalPreference) {
    prompt += ` The job seeker has personal values: [ ${custInfo.functionalPreference} ]. `;
  }
  return prompt.concat(`Job Title: [${jobInfo.jobTitle}], Hiring Company: [${jobInfo.company}], Job Description: [${jobInfo.jobDescription}] `);
}
function buildSystemPrompt() {
  return PROMPTS.cover_letter.v2 + exampleFormatting + example;
}

async function fetchWithRetry(req, init) {
  const retries = 3;
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await fetch(req, init);
    } catch (error) {
      if (init?.signal?.aborted) {
        throw error;
      }
      if (attempt === retries) {
        console.error(`Error fetching request ${req}`, error, init);
        throw error;
      }
      console.warn(`Retrying fetch attempt ${attempt + 1} for request: ${req}`);
    }
  }
  throw new Error("Unreachable code");
}

const serverSupabaseClient = async (event) => {
  if (!event.context._supabaseClient) {
    const {
      supabase: {
        url,
        key,
        cookieOptions,
        clientOptions: { auth = {}, global = {} }
      }
    } = useRuntimeConfig().public;
    event.context._supabaseClient = createServerClient(url, key, {
      auth,
      cookies: {
        getAll: () => parseCookieHeader(getHeader(event, "Cookie") ?? ""),
        setAll: (cookies) => cookies.forEach(({ name, value, options }) => setCookie(event, name, value, options))
      },
      cookieOptions,
      global: {
        fetch: fetchWithRetry,
        ...global
      }
    });
  }
  return event.context._supabaseClient;
};

const regenerateCoverLetter = defineEventHandler(async (event) => {
  console.log("generating cover letter");
  const supabase = await serverSupabaseClient(event);
  const cvId = getQuery$1(event).cvId;
  const jobId = getQuery$1(event).jobId;
  const customerJobId = getQuery$1(event).customerJobId;
  if (!cvId || !jobId || !customerJobId) {
    setResponseStatus(event, 400);
    return "BAD_REQUEST";
  }
  const { data: jobData, error: jobError } = await supabase.from("customer_jobs").select("*, job:jobs(*)").eq("id", Number(customerJobId.toString())).eq("job.id", jobId.toString()).single();
  if (jobError) {
    throw jobError;
  }
  const customerInfo = await buildCustInfo(jobData.customer_id, cvId.toString(), supabase);
  const jobInfo = {
    jobDescription: jobData.job.description,
    jobTitle: jobData.job.title,
    company: jobData.job.employer
  };
  const coverLetter = await generateCoverLetter(customerInfo, jobInfo);
  await saveCoverLetter(coverLetter, Number(customerJobId.valueOf()), supabase);
  setResponseStatus(event, 200);
  return "OK";
});
async function buildCustInfo(customerId, cvId, supabase) {
  var _a;
  const { data: customerData, error: customerError } = await supabase.from("customer_onboarding").select("*").eq("customer_id", customerId).single();
  if (customerError) {
    console.log("customer has no onboarding", customerError);
  }
  const cv = await getCV(cvId, supabase);
  return {
    cvs: [cv],
    functionalPreference: (_a = customerData == null ? void 0 : customerData.functional_preferences) == null ? void 0 : _a.join(",")
  };
}
async function getCV(cvId, supabase) {
  const { data: document, error: documentError } = await supabase.from("documents").select("*").eq("id", cvId.toString()).single();
  if (documentError) {
    throw documentError;
  }
  const cvs = await fetchDocument(document.bucket, document.path, supabase);
  console.log("cvs text", cvs);
  return cvs;
}
async function fetchDocument(bucket, path, supabase) {
  const { data, error } = await supabase.storage.from(bucket).download(path);
  if (error) {
    throw error;
  }
  return data;
}
async function saveCoverLetter(coverLetter, customerJobId, supabase) {
  const { error: saveError } = await supabase.from("cover_letters").insert({ customer_job_id: customerJobId, data: coverLetter });
  if (saveError) {
    throw saveError;
  }
}

const regenerateCoverLetter$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: regenerateCoverLetter
});

const currenciesData = [
	{
		symbol: "$",
		name: "US Dollar",
		symbol_native: "$",
		decimal_digits: 2,
		rounding: 0,
		code: "USD",
		name_plural: "US dollars"
	},
	{
		symbol: "CA$",
		name: "Canadian Dollar",
		symbol_native: "$",
		decimal_digits: 2,
		rounding: 0,
		code: "CAD",
		name_plural: "Canadian dollars"
	},
	{
		symbol: "€",
		name: "Euro",
		symbol_native: "€",
		decimal_digits: 2,
		rounding: 0,
		code: "EUR",
		name_plural: "euros"
	},
	{
		symbol: "AED",
		name: "United Arab Emirates Dirham",
		symbol_native: "د.إ.‏",
		decimal_digits: 2,
		rounding: 0,
		code: "AED",
		name_plural: "UAE dirhams"
	},
	{
		symbol: "CHF",
		name: "Swiss Franc",
		symbol_native: "CHF",
		decimal_digits: 2,
		rounding: 0.05,
		code: "CHF",
		name_plural: "Swiss francs"
	},
	{
		symbol: "CN¥",
		name: "Chinese Yuan",
		symbol_native: "CN¥",
		decimal_digits: 2,
		rounding: 0,
		code: "CNY",
		name_plural: "Chinese yuan"
	},
	{
		symbol: "£",
		name: "British Pound Sterling",
		symbol_native: "£",
		decimal_digits: 2,
		rounding: 0,
		code: "GBP",
		name_plural: "British pounds sterling"
	},
	{
		symbol: "¥",
		name: "Japanese Yen",
		symbol_native: "￥",
		decimal_digits: 0,
		rounding: 0,
		code: "JPY",
		name_plural: "Japanese yen"
	},
	{
		symbol: "PKRs",
		name: "Pakistani Rupee",
		symbol_native: "₨",
		decimal_digits: 0,
		rounding: 0,
		code: "PKR",
		name_plural: "Pakistani rupees"
	},
	{
		symbol: "S$",
		name: "Singapore Dollar",
		symbol_native: "$",
		decimal_digits: 2,
		rounding: 0,
		code: "SGD",
		name_plural: "Singapore dollars"
	}
];

const countriesData = [
	{
		name: "Afghanistan",
		unicode: "U+1F1E6 U+1F1EB",
		emoji: "🇦🇫",
		alpha2: "AF",
		dialCode: "93",
		alpha3: "AFG",
		region: "Asia",
		capital: "Kabul",
		geo: {
			lat: 33,
			long: 33
		},
		timezones: [
			"Asia/Kabul"
		]
	},
	{
		name: "Albania",
		unicode: "U+1F1E6 U+1F1F1",
		emoji: "🇦🇱",
		alpha2: "AL",
		dialCode: "355",
		alpha3: "ALB",
		region: "Europe",
		capital: "Tirana",
		geo: {
			lat: 41,
			long: 41
		},
		timezones: [
			"Europe/Tirane"
		]
	},
	{
		name: "Algeria",
		unicode: "U+1F1E9 U+1F1FF",
		emoji: "🇩🇿",
		alpha2: "DZ",
		dialCode: "213",
		alpha3: "DZA",
		region: "Africa",
		capital: "Algiers",
		geo: {
			lat: 28,
			long: 28
		},
		timezones: [
			"Africa/Algiers"
		]
	},
	{
		name: "American Samoa",
		unicode: "U+1F1E6 U+1F1F8",
		emoji: "🇦🇸",
		alpha2: "AS",
		dialCode: "1 684",
		alpha3: "ASM",
		region: "Oceania",
		capital: "Pago Pago",
		geo: {
			lat: -14.33333333,
			long: -14.33333333
		},
		timezones: [
			"Pacific/Pago_Pago"
		]
	},
	{
		name: "Andorra",
		unicode: "U+1F1E6 U+1F1E9",
		emoji: "🇦🇩",
		alpha2: "AD",
		dialCode: "376",
		alpha3: "AND",
		region: "Europe",
		capital: "Andorra la Vella",
		geo: {
			lat: 42.5,
			long: 42.5
		},
		timezones: [
			"Europe/Andorra"
		]
	},
	{
		name: "Angola",
		unicode: "U+1F1E6 U+1F1F4",
		emoji: "🇦🇴",
		alpha2: "AO",
		dialCode: "244",
		alpha3: "AGO",
		region: "Africa",
		capital: "Luanda",
		geo: {
			lat: -12.5,
			long: -12.5
		},
		timezones: [
			"Africa/Luanda"
		]
	},
	{
		name: "Anguilla",
		unicode: "U+1F1E6 U+1F1EE",
		emoji: "🇦🇮",
		alpha2: "AI",
		dialCode: "1 264",
		alpha3: "AIA",
		region: "Americas",
		capital: "The Valley",
		geo: {
			lat: 18.25,
			long: 18.25
		},
		timezones: [
			"America/Anguilla"
		]
	},
	{
		name: "Antarctica",
		unicode: "U+1F1E6 U+1F1F6",
		emoji: "🇦🇶",
		alpha2: "AQ",
		dialCode: "",
		alpha3: "ATA",
		region: "",
		capital: null,
		geo: {
			lat: -90,
			long: -90
		},
		timezones: [
			"Antarctica/McMurdo",
			"Antarctica/Casey",
			"Antarctica/Davis",
			"Antarctica/DumontDUrville",
			"Antarctica/Mawson",
			"Antarctica/Palmer",
			"Antarctica/Rothera",
			"Antarctica/Syowa",
			"Antarctica/Troll",
			"Antarctica/Vostok"
		]
	},
	{
		name: "Antigua and Barbuda",
		unicode: "U+1F1E6 U+1F1EC",
		emoji: "🇦🇬",
		alpha2: "AG",
		dialCode: "1268",
		alpha3: "ATG",
		region: "Americas",
		capital: "Saint John's",
		geo: {
			lat: 17.05,
			long: 17.05
		},
		timezones: [
			"America/Antigua"
		]
	},
	{
		name: "Argentina",
		unicode: "U+1F1E6 U+1F1F7",
		emoji: "🇦🇷",
		alpha2: "AR",
		dialCode: "54",
		alpha3: "ARG",
		region: "Americas",
		capital: "Buenos Aires",
		geo: {
			lat: -34,
			long: -34
		},
		timezones: [
			"America/Argentina/Buenos_Aires",
			"America/Argentina/Cordoba",
			"America/Argentina/Salta",
			"America/Argentina/Jujuy",
			"America/Argentina/Tucuman",
			"America/Argentina/Catamarca",
			"America/Argentina/La_Rioja",
			"America/Argentina/San_Juan",
			"America/Argentina/Mendoza",
			"America/Argentina/San_Luis",
			"America/Argentina/Rio_Gallegos",
			"America/Argentina/Ushuaia"
		]
	},
	{
		name: "Armenia",
		unicode: "U+1F1E6 U+1F1F2",
		emoji: "🇦🇲",
		alpha2: "AM",
		dialCode: "374",
		alpha3: "ARM",
		region: "Asia",
		capital: "Yerevan",
		geo: {
			lat: 40,
			long: 40
		},
		timezones: [
			"Asia/Yerevan"
		]
	},
	{
		name: "Aruba",
		unicode: "U+1F1E6 U+1F1FC",
		emoji: "🇦🇼",
		alpha2: "AW",
		dialCode: "297",
		alpha3: "ABW",
		region: "Americas",
		capital: "Oranjestad",
		geo: {
			lat: 12.5,
			long: 12.5
		},
		timezones: [
			"America/Aruba"
		]
	},
	{
		name: "Australia",
		unicode: "U+1F1E6 U+1F1FA",
		emoji: "🇦🇺",
		alpha2: "AU",
		dialCode: "61",
		alpha3: "AUS",
		region: "Oceania",
		capital: "Canberra",
		geo: {
			lat: -27,
			long: -27
		},
		timezones: [
			"Australia/Lord_Howe",
			"Antarctica/Macquarie",
			"Australia/Hobart",
			"Australia/Currie",
			"Australia/Melbourne",
			"Australia/Sydney",
			"Australia/Broken_Hill",
			"Australia/Brisbane",
			"Australia/Lindeman",
			"Australia/Adelaide",
			"Australia/Darwin",
			"Australia/Perth",
			"Australia/Eucla"
		]
	},
	{
		name: "Austria",
		unicode: "U+1F1E6 U+1F1F9",
		emoji: "🇦🇹",
		alpha2: "AT",
		dialCode: "43",
		alpha3: "AUT",
		region: "Europe",
		capital: "Vienna",
		geo: {
			lat: 47.33333333,
			long: 47.33333333
		},
		timezones: [
			"Europe/Vienna"
		]
	},
	{
		name: "Azerbaijan",
		unicode: "U+1F1E6 U+1F1FF",
		emoji: "🇦🇿",
		alpha2: "AZ",
		dialCode: "994",
		alpha3: "AZE",
		region: "Asia",
		capital: "Baku",
		geo: {
			lat: 40.5,
			long: 40.5
		},
		timezones: [
			"Asia/Baku"
		]
	},
	{
		name: "Bahamas",
		unicode: "U+1F1E7 U+1F1F8",
		emoji: "🇧🇸",
		alpha2: "BS",
		dialCode: "1 242",
		alpha3: "BHS",
		region: "Americas",
		capital: "Nassau",
		geo: {
			lat: 24.25,
			long: 24.25
		},
		timezones: [
			"America/Nassau"
		]
	},
	{
		name: "Bahrain",
		unicode: "U+1F1E7 U+1F1ED",
		emoji: "🇧🇭",
		alpha2: "BH",
		dialCode: "973",
		alpha3: "BHR",
		region: "Asia",
		capital: "Manama",
		geo: {
			lat: 26,
			long: 26
		},
		timezones: [
			"Asia/Bahrain"
		]
	},
	{
		name: "Bangladesh",
		unicode: "U+1F1E7 U+1F1E9",
		emoji: "🇧🇩",
		alpha2: "BD",
		dialCode: "880",
		alpha3: "BGD",
		region: "Asia",
		capital: "Dhaka",
		geo: {
			lat: 24,
			long: 24
		},
		timezones: [
			"Asia/Dhaka"
		]
	},
	{
		name: "Barbados",
		unicode: "U+1F1E7 U+1F1E7",
		emoji: "🇧🇧",
		alpha2: "BB",
		dialCode: "1 246",
		alpha3: "BRB",
		region: "Americas",
		capital: "Bridgetown",
		geo: {
			lat: 13.16666666,
			long: 13.16666666
		},
		timezones: [
			"America/Barbados"
		]
	},
	{
		name: "Belarus",
		unicode: "U+1F1E7 U+1F1FE",
		emoji: "🇧🇾",
		alpha2: "BY",
		dialCode: "375",
		alpha3: "BLR",
		region: "Europe",
		capital: "Minsk",
		geo: {
			lat: 53,
			long: 53
		},
		timezones: [
			"Europe/Minsk"
		]
	},
	{
		name: "Belgium",
		unicode: "U+1F1E7 U+1F1EA",
		emoji: "🇧🇪",
		alpha2: "BE",
		dialCode: "32",
		alpha3: "BEL",
		region: "Europe",
		capital: "Brussels",
		geo: {
			lat: 50.83333333,
			long: 50.83333333
		},
		timezones: [
			"Europe/Brussels"
		]
	},
	{
		name: "Belize",
		unicode: "U+1F1E7 U+1F1FF",
		emoji: "🇧🇿",
		alpha2: "BZ",
		dialCode: "501",
		alpha3: "BLZ",
		region: "Americas",
		capital: "Belmopan",
		geo: {
			lat: 17.25,
			long: 17.25
		},
		timezones: [
			"America/Belize"
		]
	},
	{
		name: "Benin",
		unicode: "U+1F1E7 U+1F1EF",
		emoji: "🇧🇯",
		alpha2: "BJ",
		dialCode: "229",
		alpha3: "BEN",
		region: "Africa",
		capital: "Porto-Novo",
		geo: {
			lat: 9.5,
			long: 9.5
		},
		timezones: [
			"Africa/Porto-Novo"
		]
	},
	{
		name: "Bermuda",
		unicode: "U+1F1E7 U+1F1F2",
		emoji: "🇧🇲",
		alpha2: "BM",
		dialCode: "1 441",
		alpha3: "BMU",
		region: "Americas",
		capital: "Hamilton",
		geo: {
			lat: 32.33333333,
			long: 32.33333333
		},
		timezones: [
			"Atlantic/Bermuda"
		]
	},
	{
		name: "Bhutan",
		unicode: "U+1F1E7 U+1F1F9",
		emoji: "🇧🇹",
		alpha2: "BT",
		dialCode: "975",
		alpha3: "BTN",
		region: "Asia",
		capital: "Thimphu",
		geo: {
			lat: 27.5,
			long: 27.5
		},
		timezones: [
			"Asia/Thimphu"
		]
	},
	{
		name: "Bolivia",
		unicode: "U+1F1E7 U+1F1F4",
		emoji: "🇧🇴",
		alpha2: "BO",
		dialCode: "591",
		alpha3: "BOL",
		region: "Americas",
		capital: "Sucre",
		geo: {
			lat: -17,
			long: -17
		},
		timezones: [
			"America/La_Paz"
		]
	},
	{
		name: "Bonaire, Sint Eustatius and Saba",
		unicode: "U+1F1E7 U+1F1F6",
		emoji: "🇧🇶",
		alpha2: "BQ",
		dialCode: "",
		alpha3: "BES",
		region: "Americas",
		geo: {
		},
		capital: "",
		timezones: [
		]
	},
	{
		name: "Bosnia and Herzegovina",
		unicode: "U+1F1E7 U+1F1E6",
		emoji: "🇧🇦",
		alpha2: "BA",
		dialCode: "387",
		alpha3: "BIH",
		region: "Europe",
		capital: "Sarajevo",
		geo: {
			lat: 44,
			long: 44
		},
		timezones: [
			"Europe/Sarajevo"
		]
	},
	{
		name: "Botswana",
		unicode: "U+1F1E7 U+1F1FC",
		emoji: "🇧🇼",
		alpha2: "BW",
		dialCode: "267",
		alpha3: "BWA",
		region: "Africa",
		capital: "Gaborone",
		geo: {
			lat: -22,
			long: -22
		},
		timezones: [
			"Africa/Gaborone"
		]
	},
	{
		name: "Bouvet Island",
		unicode: "U+1F1E7 U+1F1FB",
		emoji: "🇧🇻",
		alpha2: "BV",
		dialCode: "",
		alpha3: "BVT",
		region: "Americas",
		capital: null,
		geo: {
			lat: -54.43333333,
			long: -54.43333333
		},
		timezones: [
			"Europe/Oslo"
		]
	},
	{
		name: "Brazil",
		unicode: "U+1F1E7 U+1F1F7",
		emoji: "🇧🇷",
		alpha2: "BR",
		dialCode: "55",
		alpha3: "BRA",
		region: "Americas",
		capital: "Brasília",
		geo: {
			lat: -10,
			long: -10
		},
		timezones: [
			"America/Noronha",
			"America/Belem",
			"America/Fortaleza",
			"America/Recife",
			"America/Araguaina",
			"America/Maceio",
			"America/Bahia",
			"America/Sao_Paulo",
			"America/Campo_Grande",
			"America/Cuiaba",
			"America/Santarem",
			"America/Porto_Velho",
			"America/Boa_Vista",
			"America/Manaus",
			"America/Eirunepe",
			"America/Rio_Branco"
		]
	},
	{
		name: "British Indian Ocean Territory",
		unicode: "U+1F1EE U+1F1F4",
		emoji: "🇮🇴",
		alpha2: "IO",
		dialCode: "246",
		alpha3: "IOT",
		region: "Africa",
		capital: "Diego Garcia",
		geo: {
			lat: -6,
			long: -6
		},
		timezones: [
			"Indian/Chagos"
		]
	},
	{
		name: "Brunei Darussalam",
		unicode: "U+1F1E7 U+1F1F3",
		emoji: "🇧🇳",
		alpha2: "BN",
		dialCode: "673",
		alpha3: "BRN",
		region: "Asia",
		capital: "Bandar Seri Begawan",
		geo: {
			lat: 4.5,
			long: 4.5
		},
		timezones: [
			"Asia/Brunei"
		]
	},
	{
		name: "Bulgaria",
		unicode: "U+1F1E7 U+1F1EC",
		emoji: "🇧🇬",
		alpha2: "BG",
		dialCode: "359",
		alpha3: "BGR",
		region: "Europe",
		capital: "Sofia",
		geo: {
			lat: 43,
			long: 43
		},
		timezones: [
			"Europe/Sofia"
		]
	},
	{
		name: "Burkina Faso",
		unicode: "U+1F1E7 U+1F1EB",
		emoji: "🇧🇫",
		alpha2: "BF",
		dialCode: "226",
		alpha3: "BFA",
		region: "Africa",
		capital: "Ouagadougou",
		geo: {
			lat: 13,
			long: 13
		},
		timezones: [
			"Africa/Ouagadougou"
		]
	},
	{
		name: "Burundi",
		unicode: "U+1F1E7 U+1F1EE",
		emoji: "🇧🇮",
		alpha2: "BI",
		dialCode: "257",
		alpha3: "BDI",
		region: "Africa",
		capital: "Bujumbura",
		geo: {
			lat: -3.5,
			long: -3.5
		},
		timezones: [
			"Africa/Bujumbura"
		]
	},
	{
		name: "Cambodia",
		unicode: "U+1F1F0 U+1F1ED",
		emoji: "🇰🇭",
		alpha2: "KH",
		dialCode: "855",
		alpha3: "KHM",
		region: "Asia",
		capital: "Phnom Penh",
		geo: {
			lat: 13,
			long: 13
		},
		timezones: [
			"Asia/Phnom_Penh"
		]
	},
	{
		name: "Cameroon",
		unicode: "U+1F1E8 U+1F1F2",
		emoji: "🇨🇲",
		alpha2: "CM",
		dialCode: "237",
		alpha3: "CMR",
		region: "Africa",
		capital: "Yaoundé",
		geo: {
			lat: 6,
			long: 6
		},
		timezones: [
			"Africa/Douala"
		]
	},
	{
		name: "Canada",
		unicode: "U+1F1E8 U+1F1E6",
		emoji: "🇨🇦",
		alpha2: "CA",
		dialCode: "1",
		alpha3: "CAN",
		region: "Americas",
		capital: "Ottawa",
		geo: {
			lat: 60,
			long: 60
		},
		timezones: [
			"America/St_Johns",
			"America/Halifax",
			"America/Glace_Bay",
			"America/Moncton",
			"America/Goose_Bay",
			"America/Blanc-Sablon",
			"America/Toronto",
			"America/Nipigon",
			"America/Thunder_Bay",
			"America/Iqaluit",
			"America/Pangnirtung",
			"America/Atikokan",
			"America/Winnipeg",
			"America/Rainy_River",
			"America/Resolute",
			"America/Rankin_Inlet",
			"America/Regina",
			"America/Swift_Current",
			"America/Edmonton",
			"America/Cambridge_Bay",
			"America/Yellowknife",
			"America/Inuvik",
			"America/Creston",
			"America/Dawson_Creek",
			"America/Fort_Nelson",
			"America/Vancouver",
			"America/Whitehorse",
			"America/Dawson"
		]
	},
	{
		name: "Cape Verde",
		unicode: "U+1F1E8 U+1F1FB",
		emoji: "🇨🇻",
		alpha2: "CV",
		dialCode: "238",
		alpha3: "CPV",
		region: "Africa",
		capital: "Praia",
		geo: {
			lat: 16,
			long: 16
		},
		timezones: [
			"Atlantic/Cape_Verde"
		]
	},
	{
		name: "Cayman Islands",
		unicode: "U+1F1F0 U+1F1FE",
		emoji: "🇰🇾",
		alpha2: "KY",
		dialCode: " 345",
		alpha3: "CYM",
		region: "Americas",
		capital: "George Town",
		geo: {
			lat: 19.5,
			long: 19.5
		},
		timezones: [
			"America/Cayman"
		]
	},
	{
		name: "Central African Republic",
		unicode: "U+1F1E8 U+1F1EB",
		emoji: "🇨🇫",
		alpha2: "CF",
		dialCode: "236",
		alpha3: "CAF",
		region: "Africa",
		capital: "Bangui",
		geo: {
			lat: 7,
			long: 7
		},
		timezones: [
			"Africa/Bangui"
		]
	},
	{
		name: "Chad",
		unicode: "U+1F1F9 U+1F1E9",
		emoji: "🇹🇩",
		alpha2: "TD",
		dialCode: "235",
		alpha3: "TCD",
		region: "Africa",
		capital: "N'Djamena",
		geo: {
			lat: 15,
			long: 15
		},
		timezones: [
			"Africa/Ndjamena"
		]
	},
	{
		name: "Chile",
		unicode: "U+1F1E8 U+1F1F1",
		emoji: "🇨🇱",
		alpha2: "CL",
		dialCode: "56",
		alpha3: "CHL",
		region: "Americas",
		capital: "Santiago",
		geo: {
			lat: -30,
			long: -30
		},
		timezones: [
			"America/Santiago",
			"Pacific/Easter"
		]
	},
	{
		name: "China",
		unicode: "U+1F1E8 U+1F1F3",
		emoji: "🇨🇳",
		alpha2: "CN",
		dialCode: "86",
		alpha3: "CHN",
		region: "Asia",
		capital: "Beijing",
		geo: {
			lat: 35,
			long: 35
		},
		timezones: [
			"Asia/Shanghai",
			"Asia/Urumqi"
		]
	},
	{
		name: "Christmas Island",
		unicode: "U+1F1E8 U+1F1FD",
		emoji: "🇨🇽",
		alpha2: "CX",
		dialCode: "61",
		alpha3: "CXR",
		region: "Oceania",
		capital: "Flying Fish Cove",
		geo: {
			lat: -10.5,
			long: -10.5
		},
		timezones: [
			"Indian/Christmas"
		]
	},
	{
		name: "Cocos (Keeling) Islands",
		unicode: "U+1F1E8 U+1F1E8",
		emoji: "🇨🇨",
		alpha2: "CC",
		dialCode: "61",
		alpha3: "CCK",
		region: "Oceania",
		capital: "West Island",
		geo: {
			lat: -12.5,
			long: -12.5
		},
		timezones: [
			"Indian/Cocos"
		]
	},
	{
		name: "Colombia",
		unicode: "U+1F1E8 U+1F1F4",
		emoji: "🇨🇴",
		alpha2: "CO",
		dialCode: "57",
		alpha3: "COL",
		region: "Americas",
		capital: "Bogotá",
		geo: {
			lat: 4,
			long: 4
		},
		timezones: [
			"America/Bogota"
		]
	},
	{
		name: "Comoros",
		unicode: "U+1F1F0 U+1F1F2",
		emoji: "🇰🇲",
		alpha2: "KM",
		dialCode: "269",
		alpha3: "COM",
		region: "Africa",
		capital: "Moroni",
		geo: {
			lat: -12.16666666,
			long: -12.16666666
		},
		timezones: [
			"Indian/Comoro"
		]
	},
	{
		name: "Congo",
		unicode: "U+1F1E8 U+1F1E9",
		emoji: "🇨🇩",
		alpha2: "CD",
		dialCode: "243",
		alpha3: "COD",
		region: "Africa",
		capital: "Kinshasa",
		geo: {
			lat: 0,
			long: 0
		},
		timezones: [
			"Africa/Kinshasa",
			"Africa/Lubumbashi"
		]
	},
	{
		name: "Congo",
		unicode: "U+1F1E8 U+1F1EC",
		emoji: "🇨🇬",
		alpha2: "CG",
		dialCode: "242",
		alpha3: "COG",
		region: "Africa",
		capital: "Brazzaville",
		geo: {
			lat: -1,
			long: -1
		},
		timezones: [
			"Africa/Brazzaville"
		]
	},
	{
		name: "Cook Islands",
		unicode: "U+1F1E8 U+1F1F0",
		emoji: "🇨🇰",
		alpha2: "CK",
		dialCode: "682",
		alpha3: "COK",
		region: "Oceania",
		capital: "Avarua",
		geo: {
			lat: -21.23333333,
			long: -21.23333333
		},
		timezones: [
			"Pacific/Rarotonga"
		]
	},
	{
		name: "Costa Rica",
		unicode: "U+1F1E8 U+1F1F7",
		emoji: "🇨🇷",
		alpha2: "CR",
		dialCode: "506",
		alpha3: "CRI",
		region: "Americas",
		capital: "San José",
		geo: {
			lat: 10,
			long: 10
		},
		timezones: [
			"America/Costa_Rica"
		]
	},
	{
		name: "Croatia",
		unicode: "U+1F1ED U+1F1F7",
		emoji: "🇭🇷",
		alpha2: "HR",
		dialCode: "385",
		alpha3: "HRV",
		region: "Europe",
		capital: "Zagreb",
		geo: {
			lat: 45.16666666,
			long: 45.16666666
		},
		timezones: [
			"Europe/Zagreb"
		]
	},
	{
		name: "Cuba",
		unicode: "U+1F1E8 U+1F1FA",
		emoji: "🇨🇺",
		alpha2: "CU",
		dialCode: "53",
		alpha3: "CUB",
		region: "Americas",
		capital: "Havana",
		geo: {
			lat: 21.5,
			long: 21.5
		},
		timezones: [
			"America/Havana"
		]
	},
	{
		name: "Curaçao",
		unicode: "U+1F1E8 U+1F1FC",
		emoji: "🇨🇼",
		alpha2: "CW",
		dialCode: "",
		alpha3: "CUW",
		region: "Americas",
		capital: "Willemstad",
		geo: {
			lat: 12.116667,
			long: 12.116667
		},
		timezones: [
			"America/Curacao"
		]
	},
	{
		name: "Cyprus",
		unicode: "U+1F1E8 U+1F1FE",
		emoji: "🇨🇾",
		alpha2: "CY",
		dialCode: "537",
		alpha3: "CYP",
		region: "Asia",
		capital: "Nicosia",
		geo: {
			lat: 35,
			long: 35
		},
		timezones: [
			"Asia/Nicosia"
		]
	},
	{
		name: "Czech Republic",
		unicode: "U+1F1E8 U+1F1FF",
		emoji: "🇨🇿",
		alpha2: "CZ",
		dialCode: "420",
		alpha3: "CZE",
		region: "Europe",
		capital: "Prague",
		geo: {
			lat: 49.75,
			long: 49.75
		},
		timezones: [
			"Europe/Prague"
		]
	},
	{
		name: "Côte D'Ivoire",
		unicode: "U+1F1E8 U+1F1EE",
		emoji: "🇨🇮",
		alpha2: "CI",
		dialCode: "225",
		alpha3: "CIV",
		region: "Africa",
		capital: "Yamoussoukro",
		geo: {
			lat: 8,
			long: 8
		},
		timezones: [
			"Africa/Abidjan"
		]
	},
	{
		name: "Denmark",
		unicode: "U+1F1E9 U+1F1F0",
		emoji: "🇩🇰",
		alpha2: "DK",
		dialCode: "45",
		alpha3: "DNK",
		region: "Europe",
		capital: "Copenhagen",
		geo: {
			lat: 56,
			long: 56
		},
		timezones: [
			"Europe/Copenhagen"
		]
	},
	{
		name: "Djibouti",
		unicode: "U+1F1E9 U+1F1EF",
		emoji: "🇩🇯",
		alpha2: "DJ",
		dialCode: "253",
		alpha3: "DJI",
		region: "Africa",
		capital: "Djibouti",
		geo: {
			lat: 11.5,
			long: 11.5
		},
		timezones: [
			"Africa/Djibouti"
		]
	},
	{
		name: "Dominica",
		unicode: "U+1F1E9 U+1F1F2",
		emoji: "🇩🇲",
		alpha2: "DM",
		dialCode: "1 767",
		alpha3: "DMA",
		region: "Americas",
		capital: "Roseau",
		geo: {
			lat: 15.41666666,
			long: 15.41666666
		},
		timezones: [
			"America/Dominica"
		]
	},
	{
		name: "Dominican Republic",
		unicode: "U+1F1E9 U+1F1F4",
		emoji: "🇩🇴",
		alpha2: "DO",
		dialCode: "1 849",
		alpha3: "DOM",
		region: "Americas",
		capital: "Santo Domingo",
		geo: {
			lat: 19,
			long: 19
		},
		timezones: [
			"America/Santo_Domingo"
		]
	},
	{
		name: "Ecuador",
		unicode: "U+1F1EA U+1F1E8",
		emoji: "🇪🇨",
		alpha2: "EC",
		dialCode: "593",
		alpha3: "ECU",
		region: "Americas",
		capital: "Quito",
		geo: {
			lat: -2,
			long: -2
		},
		timezones: [
			"America/Guayaquil",
			"Pacific/Galapagos"
		]
	},
	{
		name: "Egypt",
		unicode: "U+1F1EA U+1F1EC",
		emoji: "🇪🇬",
		alpha2: "EG",
		dialCode: "20",
		alpha3: "EGY",
		region: "Africa",
		capital: "Cairo",
		geo: {
			lat: 27,
			long: 27
		},
		timezones: [
			"Africa/Cairo"
		]
	},
	{
		name: "El Salvador",
		unicode: "U+1F1F8 U+1F1FB",
		emoji: "🇸🇻",
		alpha2: "SV",
		dialCode: "503",
		alpha3: "SLV",
		region: "Americas",
		capital: "San Salvador",
		geo: {
			lat: 13.83333333,
			long: 13.83333333
		},
		timezones: [
			"America/El_Salvador"
		]
	},
	{
		name: "Equatorial Guinea",
		unicode: "U+1F1EC U+1F1F6",
		emoji: "🇬🇶",
		alpha2: "GQ",
		dialCode: "240",
		alpha3: "GNQ",
		region: "Africa",
		capital: "Malabo",
		geo: {
			lat: 2,
			long: 2
		},
		timezones: [
			"Africa/Malabo"
		]
	},
	{
		name: "Eritrea",
		unicode: "U+1F1EA U+1F1F7",
		emoji: "🇪🇷",
		alpha2: "ER",
		dialCode: "291",
		alpha3: "ERI",
		region: "Africa",
		capital: "Asmara",
		geo: {
			lat: 15,
			long: 15
		},
		timezones: [
			"Africa/Asmara"
		]
	},
	{
		name: "Estonia",
		unicode: "U+1F1EA U+1F1EA",
		emoji: "🇪🇪",
		alpha2: "EE",
		dialCode: "372",
		alpha3: "EST",
		region: "Europe",
		capital: "Tallinn",
		geo: {
			lat: 59,
			long: 59
		},
		timezones: [
			"Europe/Tallinn"
		]
	},
	{
		name: "Ethiopia",
		unicode: "U+1F1EA U+1F1F9",
		emoji: "🇪🇹",
		alpha2: "ET",
		dialCode: "251",
		alpha3: "ETH",
		region: "Africa",
		capital: "Addis Ababa",
		geo: {
			lat: 8,
			long: 8
		},
		timezones: [
			"Africa/Addis_Ababa"
		]
	},
	{
		name: "European Union",
		unicode: "U+1F1EA U+1F1FA",
		emoji: "🇪🇺",
		alpha2: "EU",
		dialCode: "",
		alpha3: "",
		region: "",
		geo: {
		},
		capital: "",
		timezones: [
		]
	},
	{
		name: "Falkland Islands (Malvinas)",
		unicode: "U+1F1EB U+1F1F0",
		emoji: "🇫🇰",
		alpha2: "FK",
		dialCode: "500",
		alpha3: "FLK",
		region: "Americas",
		capital: "Stanley",
		geo: {
			lat: -51.75,
			long: -51.75
		},
		timezones: [
			"Atlantic/Stanley"
		]
	},
	{
		name: "Faroe Islands",
		unicode: "U+1F1EB U+1F1F4",
		emoji: "🇫🇴",
		alpha2: "FO",
		dialCode: "298",
		alpha3: "FRO",
		region: "Europe",
		capital: "Tórshavn",
		geo: {
			lat: 62,
			long: 62
		},
		timezones: [
			"Atlantic/Faroe"
		]
	},
	{
		name: "Fiji",
		unicode: "U+1F1EB U+1F1EF",
		emoji: "🇫🇯",
		alpha2: "FJ",
		dialCode: "679",
		alpha3: "FJI",
		region: "Oceania",
		capital: "Suva",
		geo: {
			lat: -18,
			long: -18
		},
		timezones: [
			"Pacific/Fiji"
		]
	},
	{
		name: "Finland",
		unicode: "U+1F1EB U+1F1EE",
		emoji: "🇫🇮",
		alpha2: "FI",
		dialCode: "358",
		alpha3: "FIN",
		region: "Europe",
		capital: "Helsinki",
		geo: {
			lat: 64,
			long: 64
		},
		timezones: [
			"Europe/Helsinki"
		]
	},
	{
		name: "France",
		unicode: "U+1F1EB U+1F1F7",
		emoji: "🇫🇷",
		alpha2: "FR",
		dialCode: "33",
		alpha3: "FRA",
		region: "Europe",
		capital: "Paris",
		geo: {
			lat: 46,
			long: 46
		},
		timezones: [
			"Europe/Paris"
		]
	},
	{
		name: "French Guiana",
		unicode: "U+1F1EC U+1F1EB",
		emoji: "🇬🇫",
		alpha2: "GF",
		dialCode: "594",
		alpha3: "GUF",
		region: "Americas",
		capital: "Cayenne",
		geo: {
			lat: 4,
			long: 4
		},
		timezones: [
			"America/Cayenne"
		]
	},
	{
		name: "French Polynesia",
		unicode: "U+1F1F5 U+1F1EB",
		emoji: "🇵🇫",
		alpha2: "PF",
		dialCode: "689",
		alpha3: "PYF",
		region: "Oceania",
		capital: "Papeetē",
		geo: {
			lat: -15,
			long: -15
		},
		timezones: [
			"Pacific/Tahiti",
			"Pacific/Marquesas",
			"Pacific/Gambier"
		]
	},
	{
		name: "French Southern Territories",
		unicode: "U+1F1F9 U+1F1EB",
		emoji: "🇹🇫",
		alpha2: "TF",
		dialCode: "",
		alpha3: "ATF",
		region: "Africa",
		capital: "Port-aux-Français",
		geo: {
			lat: -49.25,
			long: -49.25
		},
		timezones: [
			"Indian/Kerguelen"
		]
	},
	{
		name: "Gabon",
		unicode: "U+1F1EC U+1F1E6",
		emoji: "🇬🇦",
		alpha2: "GA",
		dialCode: "241",
		alpha3: "GAB",
		region: "Africa",
		capital: "Libreville",
		geo: {
			lat: -1,
			long: -1
		},
		timezones: [
			"Africa/Libreville"
		]
	},
	{
		name: "Gambia",
		unicode: "U+1F1EC U+1F1F2",
		emoji: "🇬🇲",
		alpha2: "GM",
		dialCode: "220",
		alpha3: "GMB",
		region: "Africa",
		capital: "Banjul",
		geo: {
			lat: 13.46666666,
			long: 13.46666666
		},
		timezones: [
			"Africa/Banjul"
		]
	},
	{
		name: "Georgia",
		unicode: "U+1F1EC U+1F1EA",
		emoji: "🇬🇪",
		alpha2: "GE",
		dialCode: "995",
		alpha3: "GEO",
		region: "Asia",
		capital: "Tbilisi",
		geo: {
			lat: 42,
			long: 42
		},
		timezones: [
			"Asia/Tbilisi"
		]
	},
	{
		name: "Germany",
		unicode: "U+1F1E9 U+1F1EA",
		emoji: "🇩🇪",
		alpha2: "DE",
		dialCode: "49",
		alpha3: "DEU",
		region: "Europe",
		capital: "Berlin",
		geo: {
			lat: 51,
			long: 51
		},
		timezones: [
			"Europe/Berlin",
			"Europe/Busingen"
		]
	},
	{
		name: "Ghana",
		unicode: "U+1F1EC U+1F1ED",
		emoji: "🇬🇭",
		alpha2: "GH",
		dialCode: "233",
		alpha3: "GHA",
		region: "Africa",
		capital: "Accra",
		geo: {
			lat: 8,
			long: 8
		},
		timezones: [
			"Africa/Accra"
		]
	},
	{
		name: "Gibraltar",
		unicode: "U+1F1EC U+1F1EE",
		emoji: "🇬🇮",
		alpha2: "GI",
		dialCode: "350",
		alpha3: "GIB",
		region: "Europe",
		capital: "Gibraltar",
		geo: {
			lat: 36.13333333,
			long: 36.13333333
		},
		timezones: [
			"Europe/Gibraltar"
		]
	},
	{
		name: "Greece",
		unicode: "U+1F1EC U+1F1F7",
		emoji: "🇬🇷",
		alpha2: "GR",
		dialCode: "30",
		alpha3: "GRC",
		region: "Europe",
		capital: "Athens",
		geo: {
			lat: 39,
			long: 39
		},
		timezones: [
			"Europe/Athens"
		]
	},
	{
		name: "Greenland",
		unicode: "U+1F1EC U+1F1F1",
		emoji: "🇬🇱",
		alpha2: "GL",
		dialCode: "299",
		alpha3: "GRL",
		region: "Americas",
		capital: "Nuuk",
		geo: {
			lat: 72,
			long: 72
		},
		timezones: [
			"America/Godthab",
			"America/Danmarkshavn",
			"America/Scoresbysund",
			"America/Thule"
		]
	},
	{
		name: "Grenada",
		unicode: "U+1F1EC U+1F1E9",
		emoji: "🇬🇩",
		alpha2: "GD",
		dialCode: "1 473",
		alpha3: "GRD",
		region: "Americas",
		capital: "St. George's",
		geo: {
			lat: 12.11666666,
			long: 12.11666666
		},
		timezones: [
			"America/Grenada"
		]
	},
	{
		name: "Guadeloupe",
		unicode: "U+1F1EC U+1F1F5",
		emoji: "🇬🇵",
		alpha2: "GP",
		dialCode: "590",
		alpha3: "GLP",
		region: "Americas",
		capital: "Basse-Terre",
		geo: {
			lat: 16.25,
			long: 16.25
		},
		timezones: [
			"America/Guadeloupe"
		]
	},
	{
		name: "Guam",
		unicode: "U+1F1EC U+1F1FA",
		emoji: "🇬🇺",
		alpha2: "GU",
		dialCode: "1 671",
		alpha3: "GUM",
		region: "Oceania",
		capital: "Hagåtña",
		geo: {
			lat: 13.46666666,
			long: 13.46666666
		},
		timezones: [
			"Pacific/Guam"
		]
	},
	{
		name: "Guatemala",
		unicode: "U+1F1EC U+1F1F9",
		emoji: "🇬🇹",
		alpha2: "GT",
		dialCode: "502",
		alpha3: "GTM",
		region: "Americas",
		capital: "Guatemala City",
		geo: {
			lat: 15.5,
			long: 15.5
		},
		timezones: [
			"America/Guatemala"
		]
	},
	{
		name: "Guernsey",
		unicode: "U+1F1EC U+1F1EC",
		emoji: "🇬🇬",
		alpha2: "GG",
		dialCode: "44",
		alpha3: "GGY",
		region: "Europe",
		capital: "St. Peter Port",
		geo: {
			lat: 49.46666666,
			long: 49.46666666
		},
		timezones: [
			"Europe/Guernsey"
		]
	},
	{
		name: "Guinea",
		unicode: "U+1F1EC U+1F1F3",
		emoji: "🇬🇳",
		alpha2: "GN",
		dialCode: "224",
		alpha3: "GIN",
		region: "Africa",
		capital: "Conakry",
		geo: {
			lat: 11,
			long: 11
		},
		timezones: [
			"Africa/Conakry"
		]
	},
	{
		name: "Guinea-Bissau",
		unicode: "U+1F1EC U+1F1FC",
		emoji: "🇬🇼",
		alpha2: "GW",
		dialCode: "245",
		alpha3: "GNB",
		region: "Africa",
		capital: "Bissau",
		geo: {
			lat: 12,
			long: 12
		},
		timezones: [
			"Africa/Bissau"
		]
	},
	{
		name: "Guyana",
		unicode: "U+1F1EC U+1F1FE",
		emoji: "🇬🇾",
		alpha2: "GY",
		dialCode: "595",
		alpha3: "GUY",
		region: "Americas",
		capital: "Georgetown",
		geo: {
			lat: 5,
			long: 5
		},
		timezones: [
			"America/Guyana"
		]
	},
	{
		name: "Haiti",
		unicode: "U+1F1ED U+1F1F9",
		emoji: "🇭🇹",
		alpha2: "HT",
		dialCode: "509",
		alpha3: "HTI",
		region: "Americas",
		capital: "Port-au-Prince",
		geo: {
			lat: 19,
			long: 19
		},
		timezones: [
			"America/Port-au-Prince"
		]
	},
	{
		name: "Heard Island and Mcdonald Islands",
		unicode: "U+1F1ED U+1F1F2",
		emoji: "🇭🇲",
		alpha2: "HM",
		dialCode: "",
		alpha3: "HMD",
		region: "Oceania",
		geo: {
		},
		capital: "",
		timezones: [
		]
	},
	{
		name: "Honduras",
		unicode: "U+1F1ED U+1F1F3",
		emoji: "🇭🇳",
		alpha2: "HN",
		dialCode: "504",
		alpha3: "HND",
		region: "Americas",
		capital: "Tegucigalpa",
		geo: {
			lat: 15,
			long: 15
		},
		timezones: [
			"America/Tegucigalpa"
		]
	},
	{
		name: "Hong Kong",
		unicode: "U+1F1ED U+1F1F0",
		emoji: "🇭🇰",
		alpha2: "HK",
		dialCode: "852",
		alpha3: "HKG",
		region: "Asia",
		capital: "City of Victoria",
		geo: {
			lat: 22.267,
			long: 22.267
		},
		timezones: [
			"Asia/Hong_Kong"
		]
	},
	{
		name: "Hungary",
		unicode: "U+1F1ED U+1F1FA",
		emoji: "🇭🇺",
		alpha2: "HU",
		dialCode: "36",
		alpha3: "HUN",
		region: "Europe",
		capital: "Budapest",
		geo: {
			lat: 47,
			long: 47
		},
		timezones: [
			"Europe/Budapest"
		]
	},
	{
		name: "Iceland",
		unicode: "U+1F1EE U+1F1F8",
		emoji: "🇮🇸",
		alpha2: "IS",
		dialCode: "354",
		alpha3: "ISL",
		region: "Europe",
		capital: "Reykjavik",
		geo: {
			lat: 65,
			long: 65
		},
		timezones: [
			"Atlantic/Reykjavik"
		]
	},
	{
		name: "India",
		unicode: "U+1F1EE U+1F1F3",
		emoji: "🇮🇳",
		alpha2: "IN",
		dialCode: "91",
		alpha3: "IND",
		region: "Asia",
		capital: "New Delhi",
		geo: {
			lat: 20,
			long: 20
		},
		timezones: [
			"Asia/Kolkata"
		]
	},
	{
		name: "Indonesia",
		unicode: "U+1F1EE U+1F1E9",
		emoji: "🇮🇩",
		alpha2: "ID",
		dialCode: "62",
		alpha3: "IDN",
		region: "Asia",
		capital: "Jakarta",
		geo: {
			lat: -5,
			long: -5
		},
		timezones: [
			"Asia/Jakarta",
			"Asia/Pontianak",
			"Asia/Makassar",
			"Asia/Jayapura"
		]
	},
	{
		name: "Iran",
		unicode: "U+1F1EE U+1F1F7",
		emoji: "🇮🇷",
		alpha2: "IR",
		dialCode: "98",
		alpha3: "IRN",
		region: "Asia",
		capital: "Tehran",
		geo: {
			lat: 32,
			long: 32
		},
		timezones: [
			"Asia/Tehran"
		]
	},
	{
		name: "Iraq",
		unicode: "U+1F1EE U+1F1F6",
		emoji: "🇮🇶",
		alpha2: "IQ",
		dialCode: "964",
		alpha3: "IRQ",
		region: "Asia",
		capital: "Baghdad",
		geo: {
			lat: 33,
			long: 33
		},
		timezones: [
			"Asia/Baghdad"
		]
	},
	{
		name: "Ireland",
		unicode: "U+1F1EE U+1F1EA",
		emoji: "🇮🇪",
		alpha2: "IE",
		dialCode: "353",
		alpha3: "IRL",
		region: "Europe",
		capital: "Dublin",
		geo: {
			lat: 53,
			long: 53
		},
		timezones: [
			"Europe/Dublin"
		]
	},
	{
		name: "Isle of Man",
		unicode: "U+1F1EE U+1F1F2",
		emoji: "🇮🇲",
		alpha2: "IM",
		dialCode: "44",
		alpha3: "IMN",
		region: "Europe",
		capital: "Douglas",
		geo: {
			lat: 54.25,
			long: 54.25
		},
		timezones: [
			"Europe/Isle_of_Man"
		]
	},
	{
		name: "Israel",
		unicode: "U+1F1EE U+1F1F1",
		emoji: "🇮🇱",
		alpha2: "IL",
		dialCode: "972",
		alpha3: "ISR",
		region: "Asia",
		capital: "Jerusalem",
		geo: {
			lat: 31.47,
			long: 31.47
		},
		timezones: [
			"Asia/Jerusalem"
		]
	},
	{
		name: "Italy",
		unicode: "U+1F1EE U+1F1F9",
		emoji: "🇮🇹",
		alpha2: "IT",
		dialCode: "39",
		alpha3: "ITA",
		region: "Europe",
		capital: "Rome",
		geo: {
			lat: 42.83333333,
			long: 42.83333333
		},
		timezones: [
			"Europe/Rome"
		]
	},
	{
		name: "Jamaica",
		unicode: "U+1F1EF U+1F1F2",
		emoji: "🇯🇲",
		alpha2: "JM",
		dialCode: "1 876",
		alpha3: "JAM",
		region: "Americas",
		capital: "Kingston",
		geo: {
			lat: 18.25,
			long: 18.25
		},
		timezones: [
			"America/Jamaica"
		]
	},
	{
		name: "Japan",
		unicode: "U+1F1EF U+1F1F5",
		emoji: "🇯🇵",
		alpha2: "JP",
		dialCode: "81",
		alpha3: "JPN",
		region: "Asia",
		capital: "Tokyo",
		geo: {
			lat: 36,
			long: 36
		},
		timezones: [
			"Asia/Tokyo"
		]
	},
	{
		name: "Jersey",
		unicode: "U+1F1EF U+1F1EA",
		emoji: "🇯🇪",
		alpha2: "JE",
		dialCode: "44",
		alpha3: "JEY",
		region: "Europe",
		capital: "Saint Helier",
		geo: {
			lat: 49.25,
			long: 49.25
		},
		timezones: [
			"Europe/Jersey"
		]
	},
	{
		name: "Jordan",
		unicode: "U+1F1EF U+1F1F4",
		emoji: "🇯🇴",
		alpha2: "JO",
		dialCode: "962",
		alpha3: "JOR",
		region: "Asia",
		capital: "Amman",
		geo: {
			lat: 31,
			long: 31
		},
		timezones: [
			"Asia/Amman"
		]
	},
	{
		name: "Kazakhstan",
		unicode: "U+1F1F0 U+1F1FF",
		emoji: "🇰🇿",
		alpha2: "KZ",
		dialCode: "7 7",
		alpha3: "KAZ",
		region: "Asia",
		capital: "Astana",
		geo: {
			lat: 48,
			long: 48
		},
		timezones: [
			"Asia/Almaty",
			"Asia/Qyzylorda",
			"Asia/Aqtobe",
			"Asia/Aqtau",
			"Asia/Oral"
		]
	},
	{
		name: "Kenya",
		unicode: "U+1F1F0 U+1F1EA",
		emoji: "🇰🇪",
		alpha2: "KE",
		dialCode: "254",
		alpha3: "KEN",
		region: "Africa",
		capital: "Nairobi",
		geo: {
			lat: 1,
			long: 1
		},
		timezones: [
			"Africa/Nairobi"
		]
	},
	{
		name: "Kiribati",
		unicode: "U+1F1F0 U+1F1EE",
		emoji: "🇰🇮",
		alpha2: "KI",
		dialCode: "686",
		alpha3: "KIR",
		region: "Oceania",
		capital: "South Tarawa",
		geo: {
			lat: 1.41666666,
			long: 1.41666666
		},
		timezones: [
			"Pacific/Tarawa",
			"Pacific/Enderbury",
			"Pacific/Kiritimati"
		]
	},
	{
		name: "Kosovo",
		unicode: "U+1F1FD U+1F1F0",
		emoji: "🇽🇰",
		alpha2: "XK",
		dialCode: "383",
		alpha3: "",
		region: "",
		capital: "Pristina",
		geo: {
			lat: 42.666667,
			long: 42.666667
		},
		timezones: [
			"Europe/Belgrade"
		]
	},
	{
		name: "Kuwait",
		unicode: "U+1F1F0 U+1F1FC",
		emoji: "🇰🇼",
		alpha2: "KW",
		dialCode: "965",
		alpha3: "KWT",
		region: "Asia",
		capital: "Kuwait City",
		geo: {
			lat: 29.5,
			long: 29.5
		},
		timezones: [
			"Asia/Kuwait"
		]
	},
	{
		name: "Kyrgyzstan",
		unicode: "U+1F1F0 U+1F1EC",
		emoji: "🇰🇬",
		alpha2: "KG",
		dialCode: "996",
		alpha3: "KGZ",
		region: "Asia",
		capital: "Bishkek",
		geo: {
			lat: 41,
			long: 41
		},
		timezones: [
			"Asia/Bishkek"
		]
	},
	{
		name: "Lao People's Democratic Republic",
		unicode: "U+1F1F1 U+1F1E6",
		emoji: "🇱🇦",
		alpha2: "LA",
		dialCode: "856",
		alpha3: "LAO",
		region: "Asia",
		capital: "Vientiane",
		geo: {
			lat: 18,
			long: 18
		},
		timezones: [
			"Asia/Vientiane"
		]
	},
	{
		name: "Latvia",
		unicode: "U+1F1F1 U+1F1FB",
		emoji: "🇱🇻",
		alpha2: "LV",
		dialCode: "371",
		alpha3: "LVA",
		region: "Europe",
		capital: "Riga",
		geo: {
			lat: 57,
			long: 57
		},
		timezones: [
			"Europe/Riga"
		]
	},
	{
		name: "Lebanon",
		unicode: "U+1F1F1 U+1F1E7",
		emoji: "🇱🇧",
		alpha2: "LB",
		dialCode: "961",
		alpha3: "LBN",
		region: "Asia",
		capital: "Beirut",
		geo: {
			lat: 33.83333333,
			long: 33.83333333
		},
		timezones: [
			"Asia/Beirut"
		]
	},
	{
		name: "Lesotho",
		unicode: "U+1F1F1 U+1F1F8",
		emoji: "🇱🇸",
		alpha2: "LS",
		dialCode: "266",
		alpha3: "LSO",
		region: "Africa",
		capital: "Maseru",
		geo: {
			lat: -29.5,
			long: -29.5
		},
		timezones: [
			"Africa/Maseru"
		]
	},
	{
		name: "Liberia",
		unicode: "U+1F1F1 U+1F1F7",
		emoji: "🇱🇷",
		alpha2: "LR",
		dialCode: "231",
		alpha3: "LBR",
		region: "Africa",
		capital: "Monrovia",
		geo: {
			lat: 6.5,
			long: 6.5
		},
		timezones: [
			"Africa/Monrovia"
		]
	},
	{
		name: "Libya",
		unicode: "U+1F1F1 U+1F1FE",
		emoji: "🇱🇾",
		alpha2: "LY",
		dialCode: "218",
		alpha3: "LBY",
		region: "Africa",
		capital: "Tripoli",
		geo: {
			lat: 25,
			long: 25
		},
		timezones: [
			"Africa/Tripoli"
		]
	},
	{
		name: "Liechtenstein",
		unicode: "U+1F1F1 U+1F1EE",
		emoji: "🇱🇮",
		alpha2: "LI",
		dialCode: "423",
		alpha3: "LIE",
		region: "Europe",
		capital: "Vaduz",
		geo: {
			lat: 47.26666666,
			long: 47.26666666
		},
		timezones: [
			"Europe/Vaduz"
		]
	},
	{
		name: "Lithuania",
		unicode: "U+1F1F1 U+1F1F9",
		emoji: "🇱🇹",
		alpha2: "LT",
		dialCode: "370",
		alpha3: "LTU",
		region: "Europe",
		capital: "Vilnius",
		geo: {
			lat: 56,
			long: 56
		},
		timezones: [
			"Europe/Vilnius"
		]
	},
	{
		name: "Luxembourg",
		unicode: "U+1F1F1 U+1F1FA",
		emoji: "🇱🇺",
		alpha2: "LU",
		dialCode: "352",
		alpha3: "LUX",
		region: "Europe",
		capital: "Luxembourg",
		geo: {
			lat: 49.75,
			long: 49.75
		},
		timezones: [
			"Europe/Luxembourg"
		]
	},
	{
		name: "Macao",
		unicode: "U+1F1F2 U+1F1F4",
		emoji: "🇲🇴",
		alpha2: "MO",
		dialCode: "853",
		alpha3: "MAC",
		region: "Asia",
		capital: null,
		geo: {
			lat: 22.16666666,
			long: 22.16666666
		},
		timezones: [
			"Asia/Macau"
		]
	},
	{
		name: "Macedonia",
		unicode: "U+1F1F2 U+1F1F0",
		emoji: "🇲🇰",
		alpha2: "MK",
		dialCode: "389",
		alpha3: "MKD",
		region: "Europe",
		capital: "Skopje",
		geo: {
			lat: 41.83333333,
			long: 41.83333333
		},
		timezones: [
			"Europe/Skopje"
		]
	},
	{
		name: "Madagascar",
		unicode: "U+1F1F2 U+1F1EC",
		emoji: "🇲🇬",
		alpha2: "MG",
		dialCode: "261",
		alpha3: "MDG",
		region: "Africa",
		capital: "Antananarivo",
		geo: {
			lat: -20,
			long: -20
		},
		timezones: [
			"Indian/Antananarivo"
		]
	},
	{
		name: "Malawi",
		unicode: "U+1F1F2 U+1F1FC",
		emoji: "🇲🇼",
		alpha2: "MW",
		dialCode: "265",
		alpha3: "MWI",
		region: "Africa",
		capital: "Lilongwe",
		geo: {
			lat: -13.5,
			long: -13.5
		},
		timezones: [
			"Africa/Blantyre"
		]
	},
	{
		name: "Malaysia",
		unicode: "U+1F1F2 U+1F1FE",
		emoji: "🇲🇾",
		alpha2: "MY",
		dialCode: "60",
		alpha3: "MYS",
		region: "Asia",
		capital: "Kuala Lumpur",
		geo: {
			lat: 2.5,
			long: 2.5
		},
		timezones: [
			"Asia/Kuala_Lumpur",
			"Asia/Kuching"
		]
	},
	{
		name: "Maldives",
		unicode: "U+1F1F2 U+1F1FB",
		emoji: "🇲🇻",
		alpha2: "MV",
		dialCode: "960",
		alpha3: "MDV",
		region: "Asia",
		capital: "Malé",
		geo: {
			lat: 3.25,
			long: 3.25
		},
		timezones: [
			"Indian/Maldives"
		]
	},
	{
		name: "Mali",
		unicode: "U+1F1F2 U+1F1F1",
		emoji: "🇲🇱",
		alpha2: "ML",
		dialCode: "223",
		alpha3: "MLI",
		region: "Africa",
		capital: "Bamako",
		geo: {
			lat: 17,
			long: 17
		},
		timezones: [
			"Africa/Bamako"
		]
	},
	{
		name: "Malta",
		unicode: "U+1F1F2 U+1F1F9",
		emoji: "🇲🇹",
		alpha2: "MT",
		dialCode: "356",
		alpha3: "MLT",
		region: "Europe",
		capital: "Valletta",
		geo: {
			lat: 35.83333333,
			long: 35.83333333
		},
		timezones: [
			"Europe/Malta"
		]
	},
	{
		name: "Marshall Islands",
		unicode: "U+1F1F2 U+1F1ED",
		emoji: "🇲🇭",
		alpha2: "MH",
		dialCode: "692",
		alpha3: "MHL",
		region: "Oceania",
		capital: "Majuro",
		geo: {
			lat: 9,
			long: 9
		},
		timezones: [
			"Pacific/Majuro",
			"Pacific/Kwajalein"
		]
	},
	{
		name: "Martinique",
		unicode: "U+1F1F2 U+1F1F6",
		emoji: "🇲🇶",
		alpha2: "MQ",
		dialCode: "596",
		alpha3: "MTQ",
		region: "Americas",
		capital: "Fort-de-France",
		geo: {
			lat: 14.666667,
			long: 14.666667
		},
		timezones: [
			"America/Martinique"
		]
	},
	{
		name: "Mauritania",
		unicode: "U+1F1F2 U+1F1F7",
		emoji: "🇲🇷",
		alpha2: "MR",
		dialCode: "222",
		alpha3: "MRT",
		region: "Africa",
		capital: "Nouakchott",
		geo: {
			lat: 20,
			long: 20
		},
		timezones: [
			"Africa/Nouakchott"
		]
	},
	{
		name: "Mauritius",
		unicode: "U+1F1F2 U+1F1FA",
		emoji: "🇲🇺",
		alpha2: "MU",
		dialCode: "230",
		alpha3: "MUS",
		region: "Africa",
		capital: "Port Louis",
		geo: {
			lat: -20.28333333,
			long: -20.28333333
		},
		timezones: [
			"Indian/Mauritius"
		]
	},
	{
		name: "Mayotte",
		unicode: "U+1F1FE U+1F1F9",
		emoji: "🇾🇹",
		alpha2: "YT",
		dialCode: "262",
		alpha3: "MYT",
		region: "Africa",
		capital: "Mamoudzou",
		geo: {
			lat: -12.83333333,
			long: -12.83333333
		},
		timezones: [
			"Indian/Mayotte"
		]
	},
	{
		name: "Mexico",
		unicode: "U+1F1F2 U+1F1FD",
		emoji: "🇲🇽",
		alpha2: "MX",
		dialCode: "52",
		alpha3: "MEX",
		region: "Americas",
		capital: "Mexico City",
		geo: {
			lat: 23,
			long: 23
		},
		timezones: [
			"America/Mexico_City",
			"America/Cancun",
			"America/Merida",
			"America/Monterrey",
			"America/Matamoros",
			"America/Mazatlan",
			"America/Chihuahua",
			"America/Ojinaga",
			"America/Hermosillo",
			"America/Tijuana",
			"America/Bahia_Banderas"
		]
	},
	{
		name: "Micronesia",
		unicode: "U+1F1EB U+1F1F2",
		emoji: "🇫🇲",
		alpha2: "FM",
		dialCode: "691",
		alpha3: "FSM",
		region: "Oceania",
		capital: "Palikir",
		geo: {
			lat: 6.91666666,
			long: 6.91666666
		},
		timezones: [
			"Pacific/Chuuk",
			"Pacific/Pohnpei",
			"Pacific/Kosrae"
		]
	},
	{
		name: "Moldova",
		unicode: "U+1F1F2 U+1F1E9",
		emoji: "🇲🇩",
		alpha2: "MD",
		dialCode: "373",
		alpha3: "MDA",
		region: "Europe",
		capital: "Chișinău",
		geo: {
			lat: 47,
			long: 47
		},
		timezones: [
			"Europe/Chisinau"
		]
	},
	{
		name: "Monaco",
		unicode: "U+1F1F2 U+1F1E8",
		emoji: "🇲🇨",
		alpha2: "MC",
		dialCode: "377",
		alpha3: "MCO",
		region: "Europe",
		capital: "Monaco",
		geo: {
			lat: 43.73333333,
			long: 43.73333333
		},
		timezones: [
			"Europe/Monaco"
		]
	},
	{
		name: "Mongolia",
		unicode: "U+1F1F2 U+1F1F3",
		emoji: "🇲🇳",
		alpha2: "MN",
		dialCode: "976",
		alpha3: "MNG",
		region: "Asia",
		capital: "Ulan Bator",
		geo: {
			lat: 46,
			long: 46
		},
		timezones: [
			"Asia/Ulaanbaatar",
			"Asia/Hovd",
			"Asia/Choibalsan"
		]
	},
	{
		name: "Montenegro",
		unicode: "U+1F1F2 U+1F1EA",
		emoji: "🇲🇪",
		alpha2: "ME",
		dialCode: "382",
		alpha3: "MNE",
		region: "Europe",
		capital: "Podgorica",
		geo: {
			lat: 42.5,
			long: 42.5
		},
		timezones: [
			"Europe/Podgorica"
		]
	},
	{
		name: "Montserrat",
		unicode: "U+1F1F2 U+1F1F8",
		emoji: "🇲🇸",
		alpha2: "MS",
		dialCode: "1664",
		alpha3: "MSR",
		region: "Americas",
		capital: "Plymouth",
		geo: {
			lat: 16.75,
			long: 16.75
		},
		timezones: [
			"America/Montserrat"
		]
	},
	{
		name: "Morocco",
		unicode: "U+1F1F2 U+1F1E6",
		emoji: "🇲🇦",
		alpha2: "MA",
		dialCode: "212",
		alpha3: "MAR",
		region: "Africa",
		capital: "Rabat",
		geo: {
			lat: 32,
			long: 32
		},
		timezones: [
			"Africa/Casablanca"
		]
	},
	{
		name: "Mozambique",
		unicode: "U+1F1F2 U+1F1FF",
		emoji: "🇲🇿",
		alpha2: "MZ",
		dialCode: "258",
		alpha3: "MOZ",
		region: "Africa",
		capital: "Maputo",
		geo: {
			lat: -18.25,
			long: -18.25
		},
		timezones: [
			"Africa/Maputo"
		]
	},
	{
		name: "Myanmar",
		unicode: "U+1F1F2 U+1F1F2",
		emoji: "🇲🇲",
		alpha2: "MM",
		dialCode: "95",
		alpha3: "MMR",
		region: "Asia",
		capital: "Naypyidaw",
		geo: {
			lat: 22,
			long: 22
		},
		timezones: [
			"Asia/Rangoon"
		]
	},
	{
		name: "Namibia",
		unicode: "U+1F1F3 U+1F1E6",
		emoji: "🇳🇦",
		alpha2: "NA",
		dialCode: "264",
		alpha3: "NAM",
		region: "Africa",
		capital: "Windhoek",
		geo: {
			lat: -22,
			long: -22
		},
		timezones: [
			"Africa/Windhoek"
		]
	},
	{
		name: "Nauru",
		unicode: "U+1F1F3 U+1F1F7",
		emoji: "🇳🇷",
		alpha2: "NR",
		dialCode: "674",
		alpha3: "NRU",
		region: "Oceania",
		capital: "Yaren",
		geo: {
			lat: -0.53333333,
			long: -0.53333333
		},
		timezones: [
			"Pacific/Nauru"
		]
	},
	{
		name: "Nepal",
		unicode: "U+1F1F3 U+1F1F5",
		emoji: "🇳🇵",
		alpha2: "NP",
		dialCode: "977",
		alpha3: "NPL",
		region: "Asia",
		capital: "Kathmandu",
		geo: {
			lat: 28,
			long: 28
		},
		timezones: [
			"Asia/Kathmandu"
		]
	},
	{
		name: "Netherlands",
		unicode: "U+1F1F3 U+1F1F1",
		emoji: "🇳🇱",
		alpha2: "NL",
		dialCode: "31",
		alpha3: "NLD",
		region: "Europe",
		capital: "Amsterdam",
		geo: {
			lat: 52.5,
			long: 52.5
		},
		timezones: [
			"Europe/Amsterdam"
		]
	},
	{
		name: "New Caledonia",
		unicode: "U+1F1F3 U+1F1E8",
		emoji: "🇳🇨",
		alpha2: "NC",
		dialCode: "687",
		alpha3: "NCL",
		region: "Oceania",
		capital: "Nouméa",
		geo: {
			lat: -21.5,
			long: -21.5
		},
		timezones: [
			"Pacific/Noumea"
		]
	},
	{
		name: "New Zealand",
		unicode: "U+1F1F3 U+1F1FF",
		emoji: "🇳🇿",
		alpha2: "NZ",
		dialCode: "64",
		alpha3: "NZL",
		region: "Oceania",
		capital: "Wellington",
		geo: {
			lat: -41,
			long: -41
		},
		timezones: [
			"Pacific/Auckland",
			"Pacific/Chatham"
		]
	},
	{
		name: "Nicaragua",
		unicode: "U+1F1F3 U+1F1EE",
		emoji: "🇳🇮",
		alpha2: "NI",
		dialCode: "505",
		alpha3: "NIC",
		region: "Americas",
		capital: "Managua",
		geo: {
			lat: 13,
			long: 13
		},
		timezones: [
			"America/Managua"
		]
	},
	{
		name: "Niger",
		unicode: "U+1F1F3 U+1F1EA",
		emoji: "🇳🇪",
		alpha2: "NE",
		dialCode: "227",
		alpha3: "NER",
		region: "Africa",
		capital: "Niamey",
		geo: {
			lat: 16,
			long: 16
		},
		timezones: [
			"Africa/Niamey"
		]
	},
	{
		name: "Nigeria",
		unicode: "U+1F1F3 U+1F1EC",
		emoji: "🇳🇬",
		alpha2: "NG",
		dialCode: "234",
		alpha3: "NGA",
		region: "Africa",
		capital: "Abuja",
		geo: {
			lat: 10,
			long: 10
		},
		timezones: [
			"Africa/Lagos"
		]
	},
	{
		name: "Niue",
		unicode: "U+1F1F3 U+1F1FA",
		emoji: "🇳🇺",
		alpha2: "NU",
		dialCode: "683",
		alpha3: "NIU",
		region: "Oceania",
		capital: "Alofi",
		geo: {
			lat: -19.03333333,
			long: -19.03333333
		},
		timezones: [
			"Pacific/Niue"
		]
	},
	{
		name: "Norfolk Island",
		unicode: "U+1F1F3 U+1F1EB",
		emoji: "🇳🇫",
		alpha2: "NF",
		dialCode: "672",
		alpha3: "NFK",
		region: "Oceania",
		capital: "Kingston",
		geo: {
			lat: -29.03333333,
			long: -29.03333333
		},
		timezones: [
			"Pacific/Norfolk"
		]
	},
	{
		name: "North Korea",
		unicode: "U+1F1F0 U+1F1F5",
		emoji: "🇰🇵",
		alpha2: "KP",
		dialCode: "850",
		alpha3: "PRK",
		region: "Asia",
		capital: "Pyongyang",
		geo: {
			lat: 40,
			long: 40
		},
		timezones: [
			"Asia/Pyongyang"
		]
	},
	{
		name: "Northern Mariana Islands",
		unicode: "U+1F1F2 U+1F1F5",
		emoji: "🇲🇵",
		alpha2: "MP",
		dialCode: "1 670",
		alpha3: "MNP",
		region: "Oceania",
		capital: "Saipan",
		geo: {
			lat: 15.2,
			long: 15.2
		},
		timezones: [
			"Pacific/Saipan"
		]
	},
	{
		name: "Norway",
		unicode: "U+1F1F3 U+1F1F4",
		emoji: "🇳🇴",
		alpha2: "NO",
		dialCode: "47",
		alpha3: "NOR",
		region: "Europe",
		capital: "Oslo",
		geo: {
			lat: 62,
			long: 62
		},
		timezones: [
			"Europe/Oslo"
		]
	},
	{
		name: "Oman",
		unicode: "U+1F1F4 U+1F1F2",
		emoji: "🇴🇲",
		alpha2: "OM",
		dialCode: "968",
		alpha3: "OMN",
		region: "Asia",
		capital: "Muscat",
		geo: {
			lat: 21,
			long: 21
		},
		timezones: [
			"Asia/Muscat"
		]
	},
	{
		name: "Pakistan",
		unicode: "U+1F1F5 U+1F1F0",
		emoji: "🇵🇰",
		alpha2: "PK",
		dialCode: "92",
		alpha3: "PAK",
		region: "Asia",
		capital: "Islamabad",
		geo: {
			lat: 30,
			long: 30
		},
		timezones: [
			"Asia/Karachi"
		]
	},
	{
		name: "Palau",
		unicode: "U+1F1F5 U+1F1FC",
		emoji: "🇵🇼",
		alpha2: "PW",
		dialCode: "680",
		alpha3: "PLW",
		region: "Oceania",
		capital: "Ngerulmud",
		geo: {
			lat: 7.5,
			long: 7.5
		},
		timezones: [
			"Pacific/Palau"
		]
	},
	{
		name: "Palestinian Territory",
		unicode: "U+1F1F5 U+1F1F8",
		emoji: "🇵🇸",
		alpha2: "PS",
		dialCode: "970",
		alpha3: "PSE",
		region: "Asia",
		capital: "Ramallah",
		geo: {
			lat: 31.9,
			long: 31.9
		},
		timezones: [
			"Asia/Gaza",
			"Asia/Hebron"
		]
	},
	{
		name: "Panama",
		unicode: "U+1F1F5 U+1F1E6",
		emoji: "🇵🇦",
		alpha2: "PA",
		dialCode: "507",
		alpha3: "PAN",
		region: "Americas",
		capital: "Panama City",
		geo: {
			lat: 9,
			long: 9
		},
		timezones: [
			"America/Panama"
		]
	},
	{
		name: "Papua New Guinea",
		unicode: "U+1F1F5 U+1F1EC",
		emoji: "🇵🇬",
		alpha2: "PG",
		dialCode: "675",
		alpha3: "PNG",
		region: "Oceania",
		capital: "Port Moresby",
		geo: {
			lat: -6,
			long: -6
		},
		timezones: [
			"Pacific/Port_Moresby",
			"Pacific/Bougainville"
		]
	},
	{
		name: "Paraguay",
		unicode: "U+1F1F5 U+1F1FE",
		emoji: "🇵🇾",
		alpha2: "PY",
		dialCode: "595",
		alpha3: "PRY",
		region: "Americas",
		capital: "Asunción",
		geo: {
			lat: -23,
			long: -23
		},
		timezones: [
			"America/Asuncion"
		]
	},
	{
		name: "Peru",
		unicode: "U+1F1F5 U+1F1EA",
		emoji: "🇵🇪",
		alpha2: "PE",
		dialCode: "51",
		alpha3: "PER",
		region: "Americas",
		capital: "Lima",
		geo: {
			lat: -10,
			long: -10
		},
		timezones: [
			"America/Lima"
		]
	},
	{
		name: "Philippines",
		unicode: "U+1F1F5 U+1F1ED",
		emoji: "🇵🇭",
		alpha2: "PH",
		dialCode: "63",
		alpha3: "PHL",
		region: "Asia",
		capital: "Manila",
		geo: {
			lat: 13,
			long: 13
		},
		timezones: [
			"Asia/Manila"
		]
	},
	{
		name: "Pitcairn",
		unicode: "U+1F1F5 U+1F1F3",
		emoji: "🇵🇳",
		alpha2: "PN",
		dialCode: "872",
		alpha3: "PCN",
		region: "Oceania",
		capital: "Adamstown",
		geo: {
			lat: -25.06666666,
			long: -25.06666666
		},
		timezones: [
			"Pacific/Pitcairn"
		]
	},
	{
		name: "Poland",
		unicode: "U+1F1F5 U+1F1F1",
		emoji: "🇵🇱",
		alpha2: "PL",
		dialCode: "48",
		alpha3: "POL",
		region: "Europe",
		capital: "Warsaw",
		geo: {
			lat: 52,
			long: 52
		},
		timezones: [
			"Europe/Warsaw"
		]
	},
	{
		name: "Portugal",
		unicode: "U+1F1F5 U+1F1F9",
		emoji: "🇵🇹",
		alpha2: "PT",
		dialCode: "351",
		alpha3: "PRT",
		region: "Europe",
		capital: "Lisbon",
		geo: {
			lat: 39.5,
			long: 39.5
		},
		timezones: [
			"Europe/Lisbon",
			"Atlantic/Madeira",
			"Atlantic/Azores"
		]
	},
	{
		name: "Puerto Rico",
		unicode: "U+1F1F5 U+1F1F7",
		emoji: "🇵🇷",
		alpha2: "PR",
		dialCode: "1 939",
		alpha3: "PRI",
		region: "Americas",
		capital: "San Juan",
		geo: {
			lat: 18.25,
			long: 18.25
		},
		timezones: [
			"America/Puerto_Rico"
		]
	},
	{
		name: "Qatar",
		unicode: "U+1F1F6 U+1F1E6",
		emoji: "🇶🇦",
		alpha2: "QA",
		dialCode: "974",
		alpha3: "QAT",
		region: "Asia",
		capital: "Doha",
		geo: {
			lat: 25.5,
			long: 25.5
		},
		timezones: [
			"Asia/Qatar"
		]
	},
	{
		name: "Romania",
		unicode: "U+1F1F7 U+1F1F4",
		emoji: "🇷🇴",
		alpha2: "RO",
		dialCode: "40",
		alpha3: "ROU",
		region: "Europe",
		capital: "Bucharest",
		geo: {
			lat: 46,
			long: 46
		},
		timezones: [
			"Europe/Bucharest"
		]
	},
	{
		name: "Russia",
		unicode: "U+1F1F7 U+1F1FA",
		emoji: "🇷🇺",
		alpha2: "RU",
		dialCode: "7",
		alpha3: "RUS",
		region: "Europe",
		capital: "Moscow",
		geo: {
			lat: 60,
			long: 60
		},
		timezones: [
			"Europe/Kaliningrad",
			"Europe/Moscow",
			"Europe/Simferopol",
			"Europe/Volgograd",
			"Europe/Kirov",
			"Europe/Astrakhan",
			"Europe/Samara",
			"Europe/Ulyanovsk",
			"Asia/Yekaterinburg",
			"Asia/Omsk",
			"Asia/Novosibirsk",
			"Asia/Barnaul",
			"Asia/Tomsk",
			"Asia/Novokuznetsk",
			"Asia/Krasnoyarsk",
			"Asia/Irkutsk",
			"Asia/Chita",
			"Asia/Yakutsk",
			"Asia/Khandyga",
			"Asia/Vladivostok",
			"Asia/Ust-Nera",
			"Asia/Magadan",
			"Asia/Sakhalin",
			"Asia/Srednekolymsk",
			"Asia/Kamchatka",
			"Asia/Anadyr"
		]
	},
	{
		name: "Rwanda",
		unicode: "U+1F1F7 U+1F1FC",
		emoji: "🇷🇼",
		alpha2: "RW",
		dialCode: "250",
		alpha3: "RWA",
		region: "Africa",
		capital: "Kigali",
		geo: {
			lat: -2,
			long: -2
		},
		timezones: [
			"Africa/Kigali"
		]
	},
	{
		name: "Réunion",
		unicode: "U+1F1F7 U+1F1EA",
		emoji: "🇷🇪",
		alpha2: "RE",
		dialCode: "262",
		alpha3: "REU",
		region: "Africa",
		capital: "Saint-Denis",
		geo: {
			lat: -21.15,
			long: -21.15
		},
		timezones: [
			"Indian/Reunion"
		]
	},
	{
		name: "Saint Barthélemy",
		unicode: "U+1F1E7 U+1F1F1",
		emoji: "🇧🇱",
		alpha2: "BL",
		dialCode: "590",
		alpha3: "BLM",
		region: "Americas",
		capital: "Gustavia",
		geo: {
			lat: 18.5,
			long: 18.5
		},
		timezones: [
			"America/St_Barthelemy"
		]
	},
	{
		name: "Saint Helena, Ascension and Tristan Da Cunha",
		unicode: "U+1F1F8 U+1F1ED",
		emoji: "🇸🇭",
		alpha2: "SH",
		dialCode: "290",
		alpha3: "SHN",
		region: "Africa",
		geo: {
		},
		capital: "",
		timezones: [
		]
	},
	{
		name: "Saint Kitts and Nevis",
		unicode: "U+1F1F0 U+1F1F3",
		emoji: "🇰🇳",
		alpha2: "KN",
		dialCode: "1 869",
		alpha3: "KNA",
		region: "Americas",
		capital: "Basseterre",
		geo: {
			lat: 17.33333333,
			long: 17.33333333
		},
		timezones: [
			"America/St_Kitts"
		]
	},
	{
		name: "Saint Lucia",
		unicode: "U+1F1F1 U+1F1E8",
		emoji: "🇱🇨",
		alpha2: "LC",
		dialCode: "1 758",
		alpha3: "LCA",
		region: "Americas",
		capital: "Castries",
		geo: {
			lat: 13.88333333,
			long: 13.88333333
		},
		timezones: [
			"America/St_Lucia"
		]
	},
	{
		name: "Saint Martin (French Part)",
		unicode: "U+1F1F2 U+1F1EB",
		emoji: "🇲🇫",
		alpha2: "MF",
		dialCode: "590",
		alpha3: "MAF",
		region: "Americas",
		capital: "Marigot",
		geo: {
			lat: 18.08333333,
			long: 18.08333333
		},
		timezones: [
			"America/Marigot"
		]
	},
	{
		name: "Saint Pierre and Miquelon",
		unicode: "U+1F1F5 U+1F1F2",
		emoji: "🇵🇲",
		alpha2: "PM",
		dialCode: "508",
		alpha3: "SPM",
		region: "Americas",
		capital: "Saint-Pierre",
		geo: {
			lat: 46.83333333,
			long: 46.83333333
		},
		timezones: [
			"America/Miquelon"
		]
	},
	{
		name: "Saint Vincent and The Grenadines",
		unicode: "U+1F1FB U+1F1E8",
		emoji: "🇻🇨",
		alpha2: "VC",
		dialCode: "1 784",
		alpha3: "VCT",
		region: "Americas",
		capital: "Kingstown",
		geo: {
			lat: 13.25,
			long: 13.25
		},
		timezones: [
			"America/St_Vincent"
		]
	},
	{
		name: "Samoa",
		unicode: "U+1F1FC U+1F1F8",
		emoji: "🇼🇸",
		alpha2: "WS",
		dialCode: "685",
		alpha3: "WSM",
		region: "Oceania",
		capital: "Apia",
		geo: {
			lat: -13.58333333,
			long: -13.58333333
		},
		timezones: [
			"Pacific/Apia"
		]
	},
	{
		name: "San Marino",
		unicode: "U+1F1F8 U+1F1F2",
		emoji: "🇸🇲",
		alpha2: "SM",
		dialCode: "378",
		alpha3: "SMR",
		region: "Europe",
		capital: "City of San Marino",
		geo: {
			lat: 43.76666666,
			long: 43.76666666
		},
		timezones: [
			"Europe/San_Marino"
		]
	},
	{
		name: "Sao Tome and Principe",
		unicode: "U+1F1F8 U+1F1F9",
		emoji: "🇸🇹",
		alpha2: "ST",
		dialCode: "239",
		alpha3: "STP",
		region: "Africa",
		capital: "São Tomé",
		geo: {
			lat: 1,
			long: 1
		},
		timezones: [
			"Africa/Sao_Tome"
		]
	},
	{
		name: "Saudi Arabia",
		unicode: "U+1F1F8 U+1F1E6",
		emoji: "🇸🇦",
		alpha2: "SA",
		dialCode: "966",
		alpha3: "SAU",
		region: "Asia",
		capital: "Riyadh",
		geo: {
			lat: 25,
			long: 25
		},
		timezones: [
			"Asia/Riyadh"
		]
	},
	{
		name: "Senegal",
		unicode: "U+1F1F8 U+1F1F3",
		emoji: "🇸🇳",
		alpha2: "SN",
		dialCode: "221",
		alpha3: "SEN",
		region: "Africa",
		capital: "Dakar",
		geo: {
			lat: 14,
			long: 14
		},
		timezones: [
			"Africa/Dakar"
		]
	},
	{
		name: "Serbia",
		unicode: "U+1F1F7 U+1F1F8",
		emoji: "🇷🇸",
		alpha2: "RS",
		dialCode: "381",
		alpha3: "SRB",
		region: "Europe",
		capital: "Belgrade",
		geo: {
			lat: 44,
			long: 44
		},
		timezones: [
			"Europe/Belgrade"
		]
	},
	{
		name: "Seychelles",
		unicode: "U+1F1F8 U+1F1E8",
		emoji: "🇸🇨",
		alpha2: "SC",
		dialCode: "248",
		alpha3: "SYC",
		region: "Africa",
		capital: "Victoria",
		geo: {
			lat: -4.58333333,
			long: -4.58333333
		},
		timezones: [
			"Indian/Mahe"
		]
	},
	{
		name: "Sierra Leone",
		unicode: "U+1F1F8 U+1F1F1",
		emoji: "🇸🇱",
		alpha2: "SL",
		dialCode: "232",
		alpha3: "SLE",
		region: "Africa",
		capital: "Freetown",
		geo: {
			lat: 8.5,
			long: 8.5
		},
		timezones: [
			"Africa/Freetown"
		]
	},
	{
		name: "Singapore",
		unicode: "U+1F1F8 U+1F1EC",
		emoji: "🇸🇬",
		alpha2: "SG",
		dialCode: "65",
		alpha3: "SGP",
		region: "Asia",
		capital: "Singapore",
		geo: {
			lat: 1.36666666,
			long: 1.36666666
		},
		timezones: [
			"Asia/Singapore"
		]
	},
	{
		name: "Sint Maarten (Dutch Part)",
		unicode: "U+1F1F8 U+1F1FD",
		emoji: "🇸🇽",
		alpha2: "SX",
		dialCode: "",
		alpha3: "SXM",
		region: "Americas",
		capital: "Philipsburg",
		geo: {
			lat: 18.033333,
			long: 18.033333
		},
		timezones: [
			"America/Lower_Princes"
		]
	},
	{
		name: "Slovakia",
		unicode: "U+1F1F8 U+1F1F0",
		emoji: "🇸🇰",
		alpha2: "SK",
		dialCode: "421",
		alpha3: "SVK",
		region: "Europe",
		capital: "Bratislava",
		geo: {
			lat: 48.66666666,
			long: 48.66666666
		},
		timezones: [
			"Europe/Bratislava"
		]
	},
	{
		name: "Slovenia",
		unicode: "U+1F1F8 U+1F1EE",
		emoji: "🇸🇮",
		alpha2: "SI",
		dialCode: "386",
		alpha3: "SVN",
		region: "Europe",
		capital: "Ljubljana",
		geo: {
			lat: 46.11666666,
			long: 46.11666666
		},
		timezones: [
			"Europe/Ljubljana"
		]
	},
	{
		name: "Solomon Islands",
		unicode: "U+1F1F8 U+1F1E7",
		emoji: "🇸🇧",
		alpha2: "SB",
		dialCode: "677",
		alpha3: "SLB",
		region: "Oceania",
		capital: "Honiara",
		geo: {
			lat: -8,
			long: -8
		},
		timezones: [
			"Pacific/Guadalcanal"
		]
	},
	{
		name: "Somalia",
		unicode: "U+1F1F8 U+1F1F4",
		emoji: "🇸🇴",
		alpha2: "SO",
		dialCode: "252",
		alpha3: "SOM",
		region: "Africa",
		capital: "Mogadishu",
		geo: {
			lat: 10,
			long: 10
		},
		timezones: [
			"Africa/Mogadishu"
		]
	},
	{
		name: "South Africa",
		unicode: "U+1F1FF U+1F1E6",
		emoji: "🇿🇦",
		alpha2: "ZA",
		dialCode: "27",
		alpha3: "ZAF",
		region: "Africa",
		capital: "Pretoria",
		geo: {
			lat: -29,
			long: -29
		},
		timezones: [
			"Africa/Johannesburg"
		]
	},
	{
		name: "South Georgia",
		unicode: "U+1F1EC U+1F1F8",
		emoji: "🇬🇸",
		alpha2: "GS",
		dialCode: "500",
		alpha3: "SGS",
		region: "Americas",
		capital: "King Edward Point",
		geo: {
			lat: -54.5,
			long: -54.5
		},
		timezones: [
			"Atlantic/South_Georgia"
		]
	},
	{
		name: "South Korea",
		unicode: "U+1F1F0 U+1F1F7",
		emoji: "🇰🇷",
		alpha2: "KR",
		dialCode: "82",
		alpha3: "KOR",
		region: "Asia",
		capital: "Seoul",
		geo: {
			lat: 37,
			long: 37
		},
		timezones: [
			"Asia/Seoul"
		]
	},
	{
		name: "South Sudan",
		unicode: "U+1F1F8 U+1F1F8",
		emoji: "🇸🇸",
		alpha2: "SS",
		dialCode: "",
		alpha3: "SSD",
		region: "Africa",
		capital: "Juba",
		geo: {
			lat: 7,
			long: 7
		},
		timezones: [
			"Africa/Juba"
		]
	},
	{
		name: "Spain",
		unicode: "U+1F1EA U+1F1F8",
		emoji: "🇪🇸",
		alpha2: "ES",
		dialCode: "34",
		alpha3: "ESP",
		region: "Europe",
		capital: "Madrid",
		geo: {
			lat: 40,
			long: 40
		},
		timezones: [
			"Europe/Madrid",
			"Africa/Ceuta",
			"Atlantic/Canary"
		]
	},
	{
		name: "Sri Lanka",
		unicode: "U+1F1F1 U+1F1F0",
		emoji: "🇱🇰",
		alpha2: "LK",
		dialCode: "94",
		alpha3: "LKA",
		region: "Asia",
		capital: "Colombo",
		geo: {
			lat: 7,
			long: 7
		},
		timezones: [
			"Asia/Colombo"
		]
	},
	{
		name: "Sudan",
		unicode: "U+1F1F8 U+1F1E9",
		emoji: "🇸🇩",
		alpha2: "SD",
		dialCode: "249",
		alpha3: "SDN",
		region: "Africa",
		capital: "Khartoum",
		geo: {
			lat: 15,
			long: 15
		},
		timezones: [
			"Africa/Khartoum"
		]
	},
	{
		name: "Suriname",
		unicode: "U+1F1F8 U+1F1F7",
		emoji: "🇸🇷",
		alpha2: "SR",
		dialCode: "597",
		alpha3: "SUR",
		region: "Americas",
		capital: "Paramaribo",
		geo: {
			lat: 4,
			long: 4
		},
		timezones: [
			"America/Paramaribo"
		]
	},
	{
		name: "Svalbard and Jan Mayen",
		unicode: "U+1F1F8 U+1F1EF",
		emoji: "🇸🇯",
		alpha2: "SJ",
		dialCode: "47",
		alpha3: "SJM",
		region: "Europe",
		capital: "Longyearbyen",
		geo: {
			lat: 78,
			long: 78
		},
		timezones: [
			"Arctic/Longyearbyen"
		]
	},
	{
		name: "Swaziland",
		unicode: "U+1F1F8 U+1F1FF",
		emoji: "🇸🇿",
		alpha2: "SZ",
		dialCode: "268",
		alpha3: "SWZ",
		region: "Africa",
		capital: "Lobamba",
		geo: {
			lat: -26.5,
			long: -26.5
		},
		timezones: [
			"Africa/Mbabane"
		]
	},
	{
		name: "Sweden",
		unicode: "U+1F1F8 U+1F1EA",
		emoji: "🇸🇪",
		alpha2: "SE",
		dialCode: "46",
		alpha3: "SWE",
		region: "Europe",
		capital: "Stockholm",
		geo: {
			lat: 62,
			long: 62
		},
		timezones: [
			"Europe/Stockholm"
		]
	},
	{
		name: "Switzerland",
		unicode: "U+1F1E8 U+1F1ED",
		emoji: "🇨🇭",
		alpha2: "CH",
		dialCode: "41",
		alpha3: "CHE",
		region: "Europe",
		capital: "Bern",
		geo: {
			lat: 47,
			long: 47
		},
		timezones: [
			"Europe/Zurich"
		]
	},
	{
		name: "Syrian Arab Republic",
		unicode: "U+1F1F8 U+1F1FE",
		emoji: "🇸🇾",
		alpha2: "SY",
		dialCode: "963",
		alpha3: "SYR",
		region: "Asia",
		capital: "Damascus",
		geo: {
			lat: 35,
			long: 35
		},
		timezones: [
			"Asia/Damascus"
		]
	},
	{
		name: "Taiwan",
		unicode: "U+1F1F9 U+1F1FC",
		emoji: "🇹🇼",
		alpha2: "TW",
		dialCode: "886",
		alpha3: "TWN",
		region: "Asia",
		capital: "Taipei",
		geo: {
			lat: 23.5,
			long: 23.5
		},
		timezones: [
			"Asia/Taipei"
		]
	},
	{
		name: "Tajikistan",
		unicode: "U+1F1F9 U+1F1EF",
		emoji: "🇹🇯",
		alpha2: "TJ",
		dialCode: "992",
		alpha3: "TJK",
		region: "Asia",
		capital: "Dushanbe",
		geo: {
			lat: 39,
			long: 39
		},
		timezones: [
			"Asia/Dushanbe"
		]
	},
	{
		name: "Tanzania",
		unicode: "U+1F1F9 U+1F1FF",
		emoji: "🇹🇿",
		alpha2: "TZ",
		dialCode: "255",
		alpha3: "TZA",
		region: "Africa",
		capital: "Dodoma",
		geo: {
			lat: -6,
			long: -6
		},
		timezones: [
			"Africa/Dar_es_Salaam"
		]
	},
	{
		name: "Thailand",
		unicode: "U+1F1F9 U+1F1ED",
		emoji: "🇹🇭",
		alpha2: "TH",
		dialCode: "66",
		alpha3: "THA",
		region: "Asia",
		capital: "Bangkok",
		geo: {
			lat: 15,
			long: 15
		},
		timezones: [
			"Asia/Bangkok"
		]
	},
	{
		name: "Timor-Leste",
		unicode: "U+1F1F9 U+1F1F1",
		emoji: "🇹🇱",
		alpha2: "TL",
		dialCode: "670",
		alpha3: "TLS",
		region: "Asia",
		capital: "Dili",
		geo: {
			lat: -8.83333333,
			long: -8.83333333
		},
		timezones: [
			"Asia/Dili"
		]
	},
	{
		name: "Togo",
		unicode: "U+1F1F9 U+1F1EC",
		emoji: "🇹🇬",
		alpha2: "TG",
		dialCode: "228",
		alpha3: "TGO",
		region: "Africa",
		capital: "Lomé",
		geo: {
			lat: 8,
			long: 8
		},
		timezones: [
			"Africa/Lome"
		]
	},
	{
		name: "Tokelau",
		unicode: "U+1F1F9 U+1F1F0",
		emoji: "🇹🇰",
		alpha2: "TK",
		dialCode: "690",
		alpha3: "TKL",
		region: "Oceania",
		capital: "Fakaofo",
		geo: {
			lat: -9,
			long: -9
		},
		timezones: [
			"Pacific/Fakaofo"
		]
	},
	{
		name: "Tonga",
		unicode: "U+1F1F9 U+1F1F4",
		emoji: "🇹🇴",
		alpha2: "TO",
		dialCode: "676",
		alpha3: "TON",
		region: "Oceania",
		capital: "Nuku'alofa",
		geo: {
			lat: -20,
			long: -20
		},
		timezones: [
			"Pacific/Tongatapu"
		]
	},
	{
		name: "Trinidad and Tobago",
		unicode: "U+1F1F9 U+1F1F9",
		emoji: "🇹🇹",
		alpha2: "TT",
		dialCode: "1 868",
		alpha3: "TTO",
		region: "Americas",
		capital: "Port of Spain",
		geo: {
			lat: 11,
			long: 11
		},
		timezones: [
			"America/Port_of_Spain"
		]
	},
	{
		name: "Tunisia",
		unicode: "U+1F1F9 U+1F1F3",
		emoji: "🇹🇳",
		alpha2: "TN",
		dialCode: "216",
		alpha3: "TUN",
		region: "Africa",
		capital: "Tunis",
		geo: {
			lat: 34,
			long: 34
		},
		timezones: [
			"Africa/Tunis"
		]
	},
	{
		name: "Turkey",
		unicode: "U+1F1F9 U+1F1F7",
		emoji: "🇹🇷",
		alpha2: "TR",
		dialCode: "90",
		alpha3: "TUR",
		region: "Asia",
		capital: "Ankara",
		geo: {
			lat: 39,
			long: 39
		},
		timezones: [
			"Europe/Istanbul"
		]
	},
	{
		name: "Turkmenistan",
		unicode: "U+1F1F9 U+1F1F2",
		emoji: "🇹🇲",
		alpha2: "TM",
		dialCode: "993",
		alpha3: "TKM",
		region: "Asia",
		capital: "Ashgabat",
		geo: {
			lat: 40,
			long: 40
		},
		timezones: [
			"Asia/Ashgabat"
		]
	},
	{
		name: "Turks and Caicos Islands",
		unicode: "U+1F1F9 U+1F1E8",
		emoji: "🇹🇨",
		alpha2: "TC",
		dialCode: "1 649",
		alpha3: "TCA",
		region: "Americas",
		capital: "Cockburn Town",
		geo: {
			lat: 21.75,
			long: 21.75
		},
		timezones: [
			"America/Grand_Turk"
		]
	},
	{
		name: "Tuvalu",
		unicode: "U+1F1F9 U+1F1FB",
		emoji: "🇹🇻",
		alpha2: "TV",
		dialCode: "688",
		alpha3: "TUV",
		region: "Oceania",
		capital: "Funafuti",
		geo: {
			lat: -8,
			long: -8
		},
		timezones: [
			"Pacific/Funafuti"
		]
	},
	{
		name: "Uganda",
		unicode: "U+1F1FA U+1F1EC",
		emoji: "🇺🇬",
		alpha2: "UG",
		dialCode: "256",
		alpha3: "UGA",
		region: "Africa",
		capital: "Kampala",
		geo: {
			lat: 1,
			long: 1
		},
		timezones: [
			"Africa/Kampala"
		]
	},
	{
		name: "Ukraine",
		unicode: "U+1F1FA U+1F1E6",
		emoji: "🇺🇦",
		alpha2: "UA",
		dialCode: "380",
		alpha3: "UKR",
		region: "Europe",
		capital: "Kiev",
		geo: {
			lat: 49,
			long: 49
		},
		timezones: [
			"Europe/Kiev",
			"Europe/Uzhgorod",
			"Europe/Zaporozhye"
		]
	},
	{
		name: "United Arab Emirates",
		unicode: "U+1F1E6 U+1F1EA",
		emoji: "🇦🇪",
		alpha2: "AE",
		dialCode: "971",
		alpha3: "ARE",
		region: "Asia",
		capital: "Abu Dhabi",
		geo: {
			lat: 24,
			long: 24
		},
		timezones: [
			"Asia/Dubai"
		]
	},
	{
		name: "United Kingdom",
		unicode: "U+1F1EC U+1F1E7",
		emoji: "🇬🇧",
		alpha2: "GB",
		dialCode: "44",
		alpha3: "GBR",
		region: "Europe",
		capital: "London",
		geo: {
			lat: 54,
			long: 54
		},
		timezones: [
			"Europe/London"
		]
	},
	{
		name: "United States",
		unicode: "U+1F1FA U+1F1F8",
		emoji: "🇺🇸",
		alpha2: "US",
		dialCode: "1",
		alpha3: "USA",
		region: "Americas",
		capital: "Washington D.C.",
		geo: {
			lat: 38,
			long: 38
		},
		timezones: [
			"America/New_York",
			"America/Detroit",
			"America/Kentucky/Louisville",
			"America/Kentucky/Monticello",
			"America/Indiana/Indianapolis",
			"America/Indiana/Vincennes",
			"America/Indiana/Winamac",
			"America/Indiana/Marengo",
			"America/Indiana/Petersburg",
			"America/Indiana/Vevay",
			"America/Chicago",
			"America/Indiana/Tell_City",
			"America/Indiana/Knox",
			"America/Menominee",
			"America/North_Dakota/Center",
			"America/North_Dakota/New_Salem",
			"America/North_Dakota/Beulah",
			"America/Denver",
			"America/Boise",
			"America/Phoenix",
			"America/Los_Angeles",
			"America/Anchorage",
			"America/Juneau",
			"America/Sitka",
			"America/Metlakatla",
			"America/Yakutat",
			"America/Nome",
			"America/Adak",
			"Pacific/Honolulu"
		]
	},
	{
		name: "United States Minor Outlying Islands",
		unicode: "U+1F1FA U+1F1F2",
		emoji: "🇺🇲",
		alpha2: "UM",
		dialCode: "",
		alpha3: "UMI",
		region: "Oceania",
		capital: null,
		geo: {
			lat: 19.2911437,
			long: 19.2911437
		},
		timezones: [
			"Pacific/Johnston",
			"Pacific/Midway",
			"Pacific/Wake"
		]
	},
	{
		name: "Uruguay",
		unicode: "U+1F1FA U+1F1FE",
		emoji: "🇺🇾",
		alpha2: "UY",
		dialCode: "598",
		alpha3: "URY",
		region: "Americas",
		capital: "Montevideo",
		geo: {
			lat: -33,
			long: -33
		},
		timezones: [
			"America/Montevideo"
		]
	},
	{
		name: "Uzbekistan",
		unicode: "U+1F1FA U+1F1FF",
		emoji: "🇺🇿",
		alpha2: "UZ",
		dialCode: "998",
		alpha3: "UZB",
		region: "Asia",
		capital: "Tashkent",
		geo: {
			lat: 41,
			long: 41
		},
		timezones: [
			"Asia/Samarkand",
			"Asia/Tashkent"
		]
	},
	{
		name: "Vanuatu",
		unicode: "U+1F1FB U+1F1FA",
		emoji: "🇻🇺",
		alpha2: "VU",
		dialCode: "678",
		alpha3: "VUT",
		region: "Oceania",
		capital: "Port Vila",
		geo: {
			lat: -16,
			long: -16
		},
		timezones: [
			"Pacific/Efate"
		]
	},
	{
		name: "Vatican City",
		unicode: "U+1F1FB U+1F1E6",
		emoji: "🇻🇦",
		alpha2: "VA",
		dialCode: "379",
		alpha3: "VAT",
		region: "Europe",
		capital: "Vatican City",
		geo: {
			lat: 41.9,
			long: 41.9
		},
		timezones: [
			"Europe/Vatican"
		]
	},
	{
		name: "Venezuela",
		unicode: "U+1F1FB U+1F1EA",
		emoji: "🇻🇪",
		alpha2: "VE",
		dialCode: "58",
		alpha3: "VEN",
		region: "Americas",
		capital: "Caracas",
		geo: {
			lat: 8,
			long: 8
		},
		timezones: [
			"America/Caracas"
		]
	},
	{
		name: "Viet Nam",
		unicode: "U+1F1FB U+1F1F3",
		emoji: "🇻🇳",
		alpha2: "VN",
		dialCode: "84",
		alpha3: "VNM",
		region: "Asia",
		capital: "Hanoi",
		geo: {
			lat: 16.16666666,
			long: 16.16666666
		},
		timezones: [
			"Asia/Ho_Chi_Minh"
		]
	},
	{
		name: "Virgin Islands, British",
		unicode: "U+1F1FB U+1F1EC",
		emoji: "🇻🇬",
		alpha2: "VG",
		dialCode: "1 284",
		alpha3: "VGB",
		region: "Americas",
		capital: "Road Town",
		geo: {
			lat: 18.431383,
			long: 18.431383
		},
		timezones: [
			"America/Tortola"
		]
	},
	{
		name: "Virgin Islands, U.S.",
		unicode: "U+1F1FB U+1F1EE",
		emoji: "🇻🇮",
		alpha2: "VI",
		dialCode: "1 340",
		alpha3: "VIR",
		region: "Americas",
		capital: "Charlotte Amalie",
		geo: {
			lat: 18.35,
			long: 18.35
		},
		timezones: [
			"America/St_Thomas"
		]
	},
	{
		name: "Wallis and Futuna",
		unicode: "U+1F1FC U+1F1EB",
		emoji: "🇼🇫",
		alpha2: "WF",
		dialCode: "681",
		alpha3: "WLF",
		region: "Oceania",
		capital: "Mata-Utu",
		geo: {
			lat: -13.3,
			long: -13.3
		},
		timezones: [
			"Pacific/Wallis"
		]
	},
	{
		name: "Western Sahara",
		unicode: "U+1F1EA U+1F1ED",
		emoji: "🇪🇭",
		alpha2: "EH",
		dialCode: "",
		alpha3: "ESH",
		region: "Africa",
		capital: "El Aaiún",
		geo: {
			lat: 24.5,
			long: 24.5
		},
		timezones: [
			"Africa/El_Aaiun"
		]
	},
	{
		name: "Yemen",
		unicode: "U+1F1FE U+1F1EA",
		emoji: "🇾🇪",
		alpha2: "YE",
		dialCode: "967",
		alpha3: "YEM",
		region: "Asia",
		capital: "Sana'a",
		geo: {
			lat: 15,
			long: 15
		},
		timezones: [
			"Asia/Aden"
		]
	},
	{
		name: "Zambia",
		unicode: "U+1F1FF U+1F1F2",
		emoji: "🇿🇲",
		alpha2: "ZM",
		dialCode: "260",
		alpha3: "ZMB",
		region: "Africa",
		capital: "Lusaka",
		geo: {
			lat: -15,
			long: -15
		},
		timezones: [
			"Africa/Lusaka"
		]
	},
	{
		name: "Zimbabwe",
		unicode: "U+1F1FF U+1F1FC",
		emoji: "🇿🇼",
		alpha2: "ZW",
		dialCode: "263",
		alpha3: "ZWE",
		region: "Africa",
		capital: "Harare",
		geo: {
			lat: -20,
			long: -20
		},
		timezones: [
			"Africa/Harare"
		]
	},
	{
		name: "Åland Islands",
		unicode: "U+1F1E6 U+1F1FD",
		emoji: "🇦🇽",
		alpha2: "AX",
		dialCode: "",
		alpha3: "ALA",
		region: "Europe",
		capital: "Mariehamn",
		geo: {
			lat: 60.116667,
			long: 60.116667
		},
		timezones: [
			"Europe/Mariehamn"
		]
	}
];

countriesData.map((country) => ({
  label: country.name,
  value: country.alpha3
}));
var NYLAS_CUSTOMER_STATUS = /* @__PURE__ */ ((NYLAS_CUSTOMER_STATUS2) => {
  NYLAS_CUSTOMER_STATUS2["STARTED"] = "STARTED";
  NYLAS_CUSTOMER_STATUS2["AUTHENTICATED"] = "AUTHENTICATED";
  NYLAS_CUSTOMER_STATUS2["READY"] = "READY";
  NYLAS_CUSTOMER_STATUS2["REVOKED"] = "REVOKED";
  return NYLAS_CUSTOMER_STATUS2;
})(NYLAS_CUSTOMER_STATUS || {});
currenciesData.map((currency) => ({ label: `${currency.symbol_native} ${currency.code}`, value: currency.code }));

const completeEmailSetup = defineEventHandler(async (event) => {
  const supabase = await serverSupabaseClient(event);
  const customerId = getQuery$1(event).state;
  const nylasCode = getQuery$1(event).code;
  const error = getQuery$1(event).error;
  if (error) {
    const errorCode = getQuery$1(event).error_code;
    const errorDescription = getQuery$1(event).error_description;
    const errorMessage = `coulde not setup email access because: ${errorDescription}`;
    console.error(errorMessage, error, errorCode);
    return sendRedirect(event, `/customer?error_message=${errorMessage}`);
  }
  if (!nylasCode) {
    throw new Error("no nylas code");
  }
  await updateNylasCustomerCode(customerId.toString(), nylasCode.toString(), supabase);
  const grant = await exchangeCodeForGrant(customerId.toString(), nylasCode.toString(), event);
  await updateNylasCustomerGrant(customerId.toString(), grant, supabase);
  return sendRedirect(event, "/customer");
});
async function updateNylasCustomerCode(customerId, code, supabase) {
  const { error } = await supabase.from("nylas_customers").update({ code, status: NYLAS_CUSTOMER_STATUS.AUTHENTICATED }).eq("customer_id", customerId);
  if (error) {
    console.log("nylas update error", error);
    throw error;
  }
}
async function updateNylasCustomerGrant(customerId, grant, supabase) {
  const { error } = await supabase.from("nylas_customers").update({ grant, status: NYLAS_CUSTOMER_STATUS.READY }).eq("customer_id", customerId);
  if (error) {
    console.error("could not update nylas customer grant because", error);
    throw error;
  }
}
async function exchangeCodeForGrant(customerId, code, event) {
  try {
    const config = useRuntimeConfig(event);
    const nylasConfig = {
      clientId: config.public.NYLAS_CLIENT_ID,
      callbackUri: `${config.public.SITE_URL}${config.public.EMAIL_SETUP_CALLBACK_URL}`,
      apiKey: config.public.NYLAS_API_KEY,
      apiUri: config.public.NYLAS_API_URL
    };
    const nylas = new Nylas({
      apiKey: nylasConfig.apiKey,
      apiUri: nylasConfig.apiUri
    });
    const response = await nylas.auth.exchangeCodeForToken({
      clientId: nylasConfig.clientId,
      clientSecret: nylasConfig.apiKey,
      redirectUri: nylasConfig.callbackUri,
      code
    });
    const { grantId } = response;
    return grantId;
  } catch (error) {
    console.error("error during fetch", error);
    throw error;
  }
}

const completeEmailSetup$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: completeEmailSetup
});

const initEmailSetup = defineEventHandler(async (event) => {
  const customerId = getQuery$1(event).customerId;
  if (!customerId) {
    throw new Error("no customer id");
  }
  const config = useRuntimeConfig(event);
  const nylasKey = config.public.NYLAS_API_KEY;
  const nylasUrl = config.public.NYLAS_API_URL;
  const nylasClientId = config.public.NYLAS_CLIENT_ID;
  const siteUrl = config.public.SITE_URL;
  const redirect = encodeURI(siteUrl + config.public.EMAIL_SETUP_CALLBACK_URL);
  const nylas = new Nylas({
    apiKey: nylasKey,
    apiUri: nylasUrl
  });
  const client = await serverSupabaseClient(event);
  const { error } = await client.from("nylas_customers").upsert(
    { "customer_id": customerId.toString() },
    { onConflict: "customer_id", ignoreDuplicates: true }
  );
  if (error) {
    console.error("could not start email setup ", error);
    throw error;
  }
  try {
    const url = nylas.auth.urlForOAuth2({
      clientId: nylasClientId,
      redirectUri: redirect,
      state: `${customerId}`
    });
    sendRedirect(event, url);
  } catch (error2) {
    console.log("could not fetch nylas auth url", error2);
    throw error2;
  }
});

const initEmailSetup$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: initEmailSetup
});

const revokeEmailAccess = defineEventHandler(async (event) => {
  const supabase = await serverSupabaseClient(event);
  const customerId = getQuery$1(event).customerId;
  if (!customerId) {
    throw new Error("invalid customer id");
  }
  const grant = await getNylasGrant(customerId.toString(), supabase);
  await revokeGrant(grant, event);
  await updateNylasCustomerRevoked(customerId.toString(), supabase);
  return "OK";
});
async function getNylasGrant(customerId, supabase) {
  const { data, error } = await supabase.from("nylas_customers").select("*").eq("customer_id", customerId);
  if (error) {
    console.log("nylas update error", error);
    throw error;
  } else if (data.length < 1) {
    throw new Error("no grant found for customer");
  }
  return data[0].grant;
}
async function updateNylasCustomerRevoked(customerId, supabase) {
  const { error } = await supabase.from("nylas_customers").update({ status: NYLAS_CUSTOMER_STATUS.REVOKED }).eq("customer_id", customerId);
  if (error) {
    console.error("could not revoke nylas customer grant because", error);
    throw error;
  }
}
async function revokeGrant(grant, event) {
  try {
    const config = useRuntimeConfig(event);
    const nylasConfig = {
      clientId: config.public.NYLAS_CLIENT_ID,
      callbackUri: `${config.public.SITE_URL}${config.public.EMAIL_SETUP_CALLBACK_URL}`,
      apiKey: config.public.NYLAS_API_KEY,
      apiUri: config.public.NYLAS_API_URL
    };
    const nylas = new Nylas({
      apiKey: nylasConfig.apiKey,
      apiUri: nylasConfig.apiUri
    });
    const response = await nylas.grants.destroy({ grantId: grant });
    console.log("response from nylas", response);
  } catch (error) {
    console.error("error during fetch", error);
  }
}

const revokeEmailAccess$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: revokeEmailAccess
});

const Vue3 = version[0] === "3";

function resolveUnref(r) {
  return typeof r === "function" ? r() : unref(r);
}
function resolveUnrefHeadInput(ref) {
  if (ref instanceof Promise || ref instanceof Date || ref instanceof RegExp)
    return ref;
  const root = resolveUnref(ref);
  if (!ref || !root)
    return root;
  if (Array.isArray(root))
    return root.map((r) => resolveUnrefHeadInput(r));
  if (typeof root === "object") {
    const resolved = {};
    for (const k in root) {
      if (!Object.prototype.hasOwnProperty.call(root, k)) {
        continue;
      }
      if (k === "titleTemplate" || k[0] === "o" && k[1] === "n") {
        resolved[k] = unref(root[k]);
        continue;
      }
      resolved[k] = resolveUnrefHeadInput(root[k]);
    }
    return resolved;
  }
  return root;
}

const VueReactivityPlugin = defineHeadPlugin({
  hooks: {
    "entries:resolve": (ctx) => {
      for (const entry of ctx.entries)
        entry.resolvedInput = resolveUnrefHeadInput(entry.input);
    }
  }
});

const headSymbol = "usehead";
function vueInstall(head) {
  const plugin = {
    install(app) {
      if (Vue3) {
        app.config.globalProperties.$unhead = head;
        app.config.globalProperties.$head = head;
        app.provide(headSymbol, head);
      }
    }
  };
  return plugin.install;
}
function createServerHead(options = {}) {
  const head = createServerHead$1(options);
  head.use(VueReactivityPlugin);
  head.install = vueInstall(head);
  return head;
}

const unheadPlugins = true ? [CapoPlugin({ track: true })] : [];

const renderSSRHeadOptions = {"omitLineBreaks":false};

globalThis.__buildAssetsURL = buildAssetsURL;
globalThis.__publicAssetsURL = publicAssetsURL;
const getClientManifest = () => import('file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/.nuxt/dist/server/client.manifest.mjs').then((r) => r.default || r).then((r) => typeof r === "function" ? r() : r);
const getServerEntry = () => import('file://C:/Users/<USER>/Desktop/workspace/ApplySquad/system/UI/.nuxt/dist/server/server.mjs').then((r) => r.default || r);
const getSSRStyles = lazyCachedFunction(() => Promise.resolve().then(function () { return styles$1; }).then((r) => r.default || r));
const getSSRRenderer = lazyCachedFunction(async () => {
  const manifest = await getClientManifest();
  if (!manifest) {
    throw new Error("client.manifest is not available");
  }
  const createSSRApp = await getServerEntry();
  if (!createSSRApp) {
    throw new Error("Server bundle is not available");
  }
  const options = {
    manifest,
    renderToString: renderToString$1,
    buildAssetsURL
  };
  const renderer = createRenderer(createSSRApp, options);
  async function renderToString$1(input, context) {
    const html = await renderToString(input, context);
    if (process.env.NUXT_VITE_NODE_OPTIONS) {
      renderer.rendererContext.updateManifest(await getClientManifest());
    }
    return APP_ROOT_OPEN_TAG + html + APP_ROOT_CLOSE_TAG;
  }
  return renderer;
});
const getSPARenderer = lazyCachedFunction(async () => {
  const manifest = await getClientManifest();
  const spaTemplate = await Promise.resolve().then(function () { return _virtual__spaTemplate; }).then((r) => r.template).catch(() => "").then((r) => {
    {
      return APP_ROOT_OPEN_TAG + r + APP_ROOT_CLOSE_TAG;
    }
  });
  const options = {
    manifest,
    renderToString: () => spaTemplate,
    buildAssetsURL
  };
  const renderer = createRenderer(() => () => {
  }, options);
  const result = await renderer.renderToString({});
  const renderToString = (ssrContext) => {
    const config = useRuntimeConfig(ssrContext.event);
    ssrContext.modules = ssrContext.modules || /* @__PURE__ */ new Set();
    ssrContext.payload.serverRendered = false;
    ssrContext.config = {
      public: config.public,
      app: config.app
    };
    return Promise.resolve(result);
  };
  return {
    rendererContext: renderer.rendererContext,
    renderToString
  };
});
const ISLAND_SUFFIX_RE = /\.json(\?.*)?$/;
async function getIslandContext(event) {
  let url = event.path || "";
  const componentParts = url.substring("/__nuxt_island".length + 1).replace(ISLAND_SUFFIX_RE, "").split("_");
  const hashId = componentParts.length > 1 ? componentParts.pop() : void 0;
  const componentName = componentParts.join("_");
  const context = event.method === "GET" ? getQuery$1(event) : await readBody(event);
  const ctx = {
    url: "/",
    ...context,
    id: hashId,
    name: componentName,
    props: destr(context.props) || {},
    slots: {},
    components: {}
  };
  return ctx;
}
const HAS_APP_TELEPORTS = !!(appTeleportAttrs.id);
const APP_TELEPORT_OPEN_TAG = HAS_APP_TELEPORTS ? `<${appTeleportTag}${propsToString(appTeleportAttrs)}>` : "";
const APP_TELEPORT_CLOSE_TAG = HAS_APP_TELEPORTS ? `</${appTeleportTag}>` : "";
const APP_ROOT_OPEN_TAG = `<${appRootTag}${propsToString(appRootAttrs)}>`;
const APP_ROOT_CLOSE_TAG = `</${appRootTag}>`;
const PAYLOAD_URL_RE = /\/_payload.json(\?.*)?$/ ;
const ROOT_NODE_REGEX = new RegExp(`^<${appRootTag}[^>]*>([\\s\\S]*)<\\/${appRootTag}>$`);
const renderer = defineRenderHandler(async (event) => {
  const nitroApp = useNitroApp();
  const ssrError = event.path.startsWith("/__nuxt_error") ? getQuery$1(event) : null;
  if (ssrError && ssrError.statusCode) {
    ssrError.statusCode = Number.parseInt(ssrError.statusCode);
  }
  if (ssrError && !("__unenv__" in event.node.req)) {
    throw createError({
      statusCode: 404,
      statusMessage: "Page Not Found: /__nuxt_error"
    });
  }
  const isRenderingIsland = event.path.startsWith("/__nuxt_island");
  const islandContext = isRenderingIsland ? await getIslandContext(event) : void 0;
  let url = ssrError?.url || islandContext?.url || event.path;
  const isRenderingPayload = PAYLOAD_URL_RE.test(url) && !isRenderingIsland;
  if (isRenderingPayload) {
    url = url.substring(0, url.lastIndexOf("/")) || "/";
    event._path = url;
    event.node.req.url = url;
  }
  const routeOptions = getRouteRules(event);
  const head = createServerHead({
    plugins: unheadPlugins
  });
  const headEntryOptions = { mode: "server" };
  if (!isRenderingIsland) {
    head.push(appHead, headEntryOptions);
  }
  const ssrContext = {
    url,
    event,
    runtimeConfig: useRuntimeConfig(event),
    noSSR: event.context.nuxt?.noSSR || routeOptions.ssr === false && !isRenderingIsland || (false),
    head,
    error: !!ssrError,
    nuxt: void 0,
    /* NuxtApp */
    payload: ssrError ? { error: ssrError } : {},
    _payloadReducers: /* @__PURE__ */ Object.create(null),
    modules: /* @__PURE__ */ new Set(),
    islandContext
  };
  const renderer = ssrContext.noSSR ? await getSPARenderer() : await getSSRRenderer();
  const _rendered = await renderer.renderToString(ssrContext).catch(async (error) => {
    if (ssrContext._renderResponse && error.message === "skipping render") {
      return {};
    }
    const _err = !ssrError && ssrContext.payload?.error || error;
    await ssrContext.nuxt?.hooks.callHook("app:error", _err);
    throw _err;
  });
  await ssrContext.nuxt?.hooks.callHook("app:rendered", { ssrContext, renderResult: _rendered });
  if (ssrContext._renderResponse) {
    return ssrContext._renderResponse;
  }
  if (ssrContext.payload?.error && !ssrError) {
    throw ssrContext.payload.error;
  }
  if (isRenderingPayload) {
    const response2 = renderPayloadResponse(ssrContext);
    return response2;
  }
  const inlinedStyles = isRenderingIsland ? await renderInlineStyles(ssrContext.modules ?? []) : [];
  const NO_SCRIPTS = routeOptions.experimentalNoScripts;
  const { styles, scripts } = getRequestDependencies(ssrContext, renderer.rendererContext);
  if (ssrContext._preloadManifest) {
    head.push({
      link: [
        { rel: "preload", as: "fetch", fetchpriority: "low", crossorigin: "anonymous", href: buildAssetsURL(`builds/meta/${ssrContext.runtimeConfig.app.buildId}.json`) }
      ]
    }, { ...headEntryOptions, tagPriority: "low" });
  }
  if (inlinedStyles.length) {
    head.push({ style: inlinedStyles });
  }
  {
    const link = [];
    for (const resource of Object.values(styles)) {
      if ("inline" in getQuery(resource.file)) {
        continue;
      }
      if (!isRenderingIsland || resource.file.includes("scoped") && !resource.file.includes("pages/")) {
        link.push({ rel: "stylesheet", href: renderer.rendererContext.buildAssetsURL(resource.file), crossorigin: "" });
      }
    }
    if (link.length) {
      head.push({ link }, headEntryOptions);
    }
  }
  if (!NO_SCRIPTS && !isRenderingIsland) {
    head.push({
      link: getPreloadLinks(ssrContext, renderer.rendererContext)
    }, headEntryOptions);
    head.push({
      link: getPrefetchLinks(ssrContext, renderer.rendererContext)
    }, headEntryOptions);
    head.push({
      script: renderPayloadJsonScript({ ssrContext, data: ssrContext.payload }) 
    }, {
      ...headEntryOptions,
      // this should come before another end of body scripts
      tagPosition: "bodyClose",
      tagPriority: "high"
    });
  }
  if (!routeOptions.experimentalNoScripts && !isRenderingIsland) {
    head.push({
      script: Object.values(scripts).map((resource) => ({
        type: resource.module ? "module" : null,
        src: renderer.rendererContext.buildAssetsURL(resource.file),
        defer: resource.module ? null : true,
        // if we are rendering script tag payloads that import an async payload
        // we need to ensure this resolves before executing the Nuxt entry
        tagPosition: "head",
        crossorigin: ""
      }))
    }, headEntryOptions);
  }
  const { headTags, bodyTags, bodyTagsOpen, htmlAttrs, bodyAttrs } = await renderSSRHead(head, renderSSRHeadOptions);
  const htmlContext = {
    island: isRenderingIsland,
    htmlAttrs: htmlAttrs ? [htmlAttrs] : [],
    head: normalizeChunks([headTags]),
    bodyAttrs: bodyAttrs ? [bodyAttrs] : [],
    bodyPrepend: normalizeChunks([bodyTagsOpen, ssrContext.teleports?.body]),
    body: [
      replaceIslandTeleports(ssrContext, _rendered.html) ,
      APP_TELEPORT_OPEN_TAG + (HAS_APP_TELEPORTS ? joinTags([ssrContext.teleports?.[`#${appTeleportAttrs.id}`]]) : "") + APP_TELEPORT_CLOSE_TAG
    ],
    bodyAppend: [bodyTags]
  };
  await nitroApp.hooks.callHook("render:html", htmlContext, { event });
  if (isRenderingIsland && islandContext) {
    const islandHead = {};
    for (const entry of head.headEntries()) {
      for (const [key, value] of Object.entries(resolveUnrefHeadInput(entry.input))) {
        const currentValue = islandHead[key];
        if (Array.isArray(currentValue)) {
          currentValue.push(...value);
        }
        islandHead[key] = value;
      }
    }
    islandHead.link ||= [];
    islandHead.style ||= [];
    const islandResponse = {
      id: islandContext.id,
      head: islandHead,
      html: getServerComponentHTML(htmlContext.body),
      components: getClientIslandResponse(ssrContext),
      slots: getSlotIslandResponse(ssrContext)
    };
    await nitroApp.hooks.callHook("render:island", islandResponse, { event, islandContext });
    const response2 = {
      body: JSON.stringify(islandResponse, null, 2),
      statusCode: getResponseStatus(event),
      statusMessage: getResponseStatusText(event),
      headers: {
        "content-type": "application/json;charset=utf-8",
        "x-powered-by": "Nuxt"
      }
    };
    return response2;
  }
  const response = {
    body: renderHTMLDocument(htmlContext),
    statusCode: getResponseStatus(event),
    statusMessage: getResponseStatusText(event),
    headers: {
      "content-type": "text/html;charset=utf-8",
      "x-powered-by": "Nuxt"
    }
  };
  return response;
});
function lazyCachedFunction(fn) {
  let res = null;
  return () => {
    if (res === null) {
      res = fn().catch((err) => {
        res = null;
        throw err;
      });
    }
    return res;
  };
}
function normalizeChunks(chunks) {
  return chunks.filter(Boolean).map((i) => i.trim());
}
function joinTags(tags) {
  return tags.join("");
}
function joinAttrs(chunks) {
  if (chunks.length === 0) {
    return "";
  }
  return " " + chunks.join(" ");
}
function renderHTMLDocument(html) {
  return `<!DOCTYPE html><html${joinAttrs(html.htmlAttrs)}><head>${joinTags(html.head)}</head><body${joinAttrs(html.bodyAttrs)}>${joinTags(html.bodyPrepend)}${joinTags(html.body)}${joinTags(html.bodyAppend)}</body></html>`;
}
async function renderInlineStyles(usedModules) {
  const styleMap = await getSSRStyles();
  const inlinedStyles = /* @__PURE__ */ new Set();
  for (const mod of usedModules) {
    if (mod in styleMap && styleMap[mod]) {
      for (const style of await styleMap[mod]()) {
        inlinedStyles.add(style);
      }
    }
  }
  return Array.from(inlinedStyles).map((style) => ({ innerHTML: style }));
}
function renderPayloadResponse(ssrContext) {
  return {
    body: stringify(splitPayload(ssrContext).payload, ssrContext._payloadReducers) ,
    statusCode: getResponseStatus(ssrContext.event),
    statusMessage: getResponseStatusText(ssrContext.event),
    headers: {
      "content-type": "application/json;charset=utf-8" ,
      "x-powered-by": "Nuxt"
    }
  };
}
function renderPayloadJsonScript(opts) {
  const contents = opts.data ? stringify(opts.data, opts.ssrContext._payloadReducers) : "";
  const payload = {
    "type": "application/json",
    "innerHTML": contents,
    "data-nuxt-data": appId,
    "data-ssr": !(opts.ssrContext.noSSR)
  };
  {
    payload.id = "__NUXT_DATA__";
  }
  if (opts.src) {
    payload["data-src"] = opts.src;
  }
  const config = uneval(opts.ssrContext.config);
  return [
    payload,
    {
      innerHTML: `window.__NUXT__={};window.__NUXT__.config=${config}`
    }
  ];
}
function splitPayload(ssrContext) {
  const { data, prerenderedAt, ...initial } = ssrContext.payload;
  return {
    initial: { ...initial, prerenderedAt },
    payload: { data, prerenderedAt }
  };
}
function getServerComponentHTML(body) {
  const match = body[0].match(ROOT_NODE_REGEX);
  return match?.[1] || body[0];
}
const SSR_SLOT_TELEPORT_MARKER = /^uid=([^;]*);slot=(.*)$/;
const SSR_CLIENT_TELEPORT_MARKER = /^uid=([^;]*);client=(.*)$/;
const SSR_CLIENT_SLOT_MARKER = /^island-slot=[^;]*;(.*)$/;
function getSlotIslandResponse(ssrContext) {
  if (!ssrContext.islandContext || !Object.keys(ssrContext.islandContext.slots).length) {
    return void 0;
  }
  const response = {};
  for (const [name, slot] of Object.entries(ssrContext.islandContext.slots)) {
    response[name] = {
      ...slot,
      fallback: ssrContext.teleports?.[`island-fallback=${name}`]
    };
  }
  return response;
}
function getClientIslandResponse(ssrContext) {
  if (!ssrContext.islandContext || !Object.keys(ssrContext.islandContext.components).length) {
    return void 0;
  }
  const response = {};
  for (const [clientUid, component] of Object.entries(ssrContext.islandContext.components)) {
    const html = ssrContext.teleports?.[clientUid]?.replaceAll("<!--teleport start anchor-->", "") || "";
    response[clientUid] = {
      ...component,
      html,
      slots: getComponentSlotTeleport(ssrContext.teleports ?? {})
    };
  }
  return response;
}
function getComponentSlotTeleport(teleports) {
  const entries = Object.entries(teleports);
  const slots = {};
  for (const [key, value] of entries) {
    const match = key.match(SSR_CLIENT_SLOT_MARKER);
    if (match) {
      const [, slot] = match;
      if (!slot) {
        continue;
      }
      slots[slot] = value;
    }
  }
  return slots;
}
function replaceIslandTeleports(ssrContext, html) {
  const { teleports, islandContext } = ssrContext;
  if (islandContext || !teleports) {
    return html;
  }
  for (const key in teleports) {
    const matchClientComp = key.match(SSR_CLIENT_TELEPORT_MARKER);
    if (matchClientComp) {
      const [, uid, clientId] = matchClientComp;
      if (!uid || !clientId) {
        continue;
      }
      html = html.replace(new RegExp(` data-island-uid="${uid}" data-island-component="${clientId}"[^>]*>`), (full) => {
        return full + teleports[key];
      });
      continue;
    }
    const matchSlot = key.match(SSR_SLOT_TELEPORT_MARKER);
    if (matchSlot) {
      const [, uid, slot] = matchSlot;
      if (!uid || !slot) {
        continue;
      }
      html = html.replace(new RegExp(` data-island-uid="${uid}" data-island-slot="${slot}"[^>]*>`), (full) => {
        return full + teleports[key];
      });
    }
  }
  return html;
}

const renderer$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: renderer
});

const styles = {};

const styles$1 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  default: styles
});

const template = "";

const _virtual__spaTemplate = /*#__PURE__*/Object.freeze({
  __proto__: null,
  template: template
});
//# sourceMappingURL=index.mjs.map
