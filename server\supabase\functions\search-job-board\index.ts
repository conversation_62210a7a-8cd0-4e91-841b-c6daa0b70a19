import {
  TOPICS,
  isValidQstashRequest,
  Events,
  LiveQstash,
  supabaseClient,
  getCustomerSearchCriteria,
  getJobBoardByKey,
  saveJobs,
  updateSearchCriteriaStatus,
  CustomerSearchCriteriaCreated,
  JobsFound,
  JOB_BOARDS
} from '../shared/common.ts';
import { theirStackClient, TheirStackClient } from './job-search-theirstack.ts';

console.log("Search Job Board Event Handler booting");

export function startDeno() {
  // just used by tests to start Deno.serve()
}

Deno.serve(async (req: Request) => {
  const body = await req.text();
  console.log("handling search job board request, received body:", body);

  if (await isValidQstashRequest(req.headers, body)) {
    try {
      // Parse the incoming payload
      const payload: CustomerSearchCriteriaCreated[] = JSON.parse(body);
      console.log(`Processing ${payload.length} search criteria events`);

      const supabase = supabaseClient();
      const jobsFoundEvents: JobsFound[] = [];

      // Process each search criteria
      for (const event of payload) {
        try {
          console.log(`Processing search criteria: ${event.customerSearchCriteriaId}`);

          // Get the search criteria from database
          const searchCriteria = await getCustomerSearchCriteria(
            event.customerSearchCriteriaId,
            supabase
          );

          // Get the job board information
          const jobBoard = await getJobBoardByKey(searchCriteria.job_board_id, supabase);
          if (!jobBoard) {
            console.error(`Job board not found: ${searchCriteria.job_board_id}`);
            continue;
          }

          console.log(`Searching ${jobBoard.key} for jobs matching:`, {
            jobTitles: searchCriteria.job_titles,
            locations: searchCriteria.locations
          });

          // Search for jobs based on the job board
          let searchResult;
          if (jobBoard.key === JOB_BOARDS.THEIR_STACK || jobBoard.key === "theirstack") {
            searchResult = await getTheirStackClient().searchJobs(
              searchCriteria.job_titles,
              searchCriteria.locations,
              { limit: 50 } // Configurable limit
            );
          } else {
            console.warn(`Unsupported job board: ${jobBoard.key}`);
            continue;
          }

          console.log(`Found ${searchResult.jobs.length} jobs from ${jobBoard.key}`);

          // Save jobs to database if any were found
          if (searchResult.jobs.length > 0) {
            await saveJobs(searchResult.jobs, supabase);

            // Create event for job matching
            jobsFoundEvents.push({
              searchCriteriaId: searchCriteria.id,
              jobIds: searchResult.jobs.map(job => job.id || `theirstack_${job.src_id}`)
            });
          }

          // Update search criteria status to COMPLETE
          await updateSearchCriteriaStatus(
            searchCriteria.id,
            "COMPLETE",
            supabase
          );

          console.log(`Completed processing search criteria: ${event.customerSearchCriteriaId}`);

        } catch (error) {
          console.error(`Error processing search criteria ${event.customerSearchCriteriaId}:`, error);

          // Still mark as complete to avoid infinite retries
          try {
            await updateSearchCriteriaStatus(
              event.customerSearchCriteriaId,
              "COMPLETE",
              supabase
            );
          } catch (updateError) {
            console.error(`Failed to update search criteria status:`, updateError);
          }
        }
      }

      // Publish jobs found events for customer-job matching
      if (jobsFoundEvents.length > 0) {
        const topic = Deno.env.get(TOPICS.MATCH_CUSTOMER_JOB)!;
        await getEvents().publishJobsFound(jobsFoundEvents, topic);
        console.log(`Published ${jobsFoundEvents.length} job found events to ${topic}`);
      }

      return new Response(`Successfully processed ${payload.length} search criteria`);

    } catch (error) {
      console.error("Error processing search job board request:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return new Response(`Error: ${errorMessage}`, { status: 500 });
    }

  } else {
    console.warn("not a valid request from Qstash, ignoring");
    return new Response("Unauthorized", { status: 401 });
  }
});

// this is to allow the test to set a mock
let _events: Events;
export function setEvents(client: Events) {
  _events = client;
}

function getEvents(): Events {
  if (!_events) {
    _events = new LiveQstash();
  }
  return _events;
}

// this is to allow the test to set a mock TheirStack client
let _theirStackClient: TheirStackClient;
export function setTheirStackClient(client: TheirStackClient) {
  _theirStackClient = client;
}

function getTheirStackClient(): TheirStackClient {
  if (!_theirStackClient) {
    _theirStackClient = theirStackClient;
  }
  return _theirStackClient;
}