{"hash": "6891ef8b", "configHash": "c661a5fe", "lockfileHash": "102737cb", "browserHash": "c3f72c67", "optimized": {"@nuxtjs/supabase > cookie": {"src": "../../../../cookie/index.js", "file": "@nuxtjs_supabase___cookie.js", "fileHash": "43c6b5f7", "needsInterop": true}, "@nuxtjs/supabase > @supabase/postgrest-js": {"src": "../../../../@supabase/postgrest-js/dist/esm/wrapper.mjs", "file": "@nuxtjs_supabase___@supabase_postgrest-js.js", "fileHash": "2044639e", "needsInterop": false}, "@vueup/vue-quill": {"src": "../../../../@vueup/vue-quill/dist/vue-quill.esm-bundler.js", "file": "@vueup_vue-quill.js", "fileHash": "10981558", "needsInterop": false}, "@intlify/shared": {"src": "../../../../@intlify/shared/dist/shared.mjs", "file": "@intlify_shared.js", "fileHash": "72f28a7c", "needsInterop": false}, "@intlify/core-base": {"src": "../../../../@intlify/core-base/dist/core-base.mjs", "file": "@intlify_core-base.js", "fileHash": "22e155e6", "needsInterop": false}, "adler-32": {"src": "../../../../adler-32/adler32.js", "file": "adler-32.js", "fileHash": "6324e74a", "needsInterop": true}}, "chunks": {"chunk-R3RWK2N4": {"file": "chunk-R3RWK2N4.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}