{"version": 3, "sources": ["../../../../adler-32/adler32.js"], "sourcesContent": ["/* adler32.js (C) 2014-present SheetJS -- http://sheetjs.com */\n/* vim: set ts=2: */\n/*exported ADLER32 */\nvar ADLER32;\n(function (factory) {\n\t/*jshint ignore:start */\n\t/*eslint-disable */\n\tif(typeof DO_NOT_EXPORT_ADLER === 'undefined') {\n\t\tif('object' === typeof exports) {\n\t\t\tfactory(exports);\n\t\t} else if ('function' === typeof define && define.amd) {\n\t\t\tdefine(function () {\n\t\t\t\tvar module = {};\n\t\t\t\tfactory(module);\n\t\t\t\treturn module;\n\t\t\t});\n\t\t} else {\n\t\t\tfactory(ADLER32 = {});\n\t\t}\n\t} else {\n\t\tfactory(ADLER32 = {});\n\t}\n\t/*eslint-enable */\n\t/*jshint ignore:end */\n}(function(ADLER32) {\nADLER32.version = '1.3.1';\nfunction adler32_bstr(bstr, seed) {\n\tvar a = 1, b = 0, L = bstr.length, M = 0;\n\tif(typeof seed === 'number') { a = seed & 0xFFFF; b = seed >>> 16; }\n\tfor(var i = 0; i < L;) {\n\t\tM = Math.min(L-i, 2654)+i;\n\t\tfor(;i<M;i++) {\n\t\t\ta += bstr.charCodeAt(i)&0xFF;\n\t\t\tb += a;\n\t\t}\n\t\ta = (15*(a>>>16)+(a&65535));\n\t\tb = (15*(b>>>16)+(b&65535));\n\t}\n\treturn ((b%65521) << 16) | (a%65521);\n}\n\nfunction adler32_buf(buf, seed) {\n\tvar a = 1, b = 0, L = buf.length, M = 0;\n\tif(typeof seed === 'number') { a = seed & 0xFFFF; b = (seed >>> 16) & 0xFFFF; }\n\tfor(var i = 0; i < L;) {\n\t\tM = Math.min(L-i, 2654)+i;\n\t\tfor(;i<M;i++) {\n\t\t\ta += buf[i]&0xFF;\n\t\t\tb += a;\n\t\t}\n\t\ta = (15*(a>>>16)+(a&65535));\n\t\tb = (15*(b>>>16)+(b&65535));\n\t}\n\treturn ((b%65521) << 16) | (a%65521);\n}\n\nfunction adler32_str(str, seed) {\n\tvar a = 1, b = 0, L = str.length, M = 0, c = 0, d = 0;\n\tif(typeof seed === 'number') { a = seed & 0xFFFF; b = seed >>> 16; }\n\tfor(var i = 0; i < L;) {\n\t\tM = Math.min(L-i, 2918);\n\t\twhile(M>0) {\n\t\t\tc = str.charCodeAt(i++);\n\t\t\tif(c < 0x80) { a += c; }\n\t\t\telse if(c < 0x800) {\n\t\t\t\ta += 192|((c>>6)&31);             b += a; --M;\n\t\t\t\ta += 128|(c&63);\n\t\t\t} else if(c >= 0xD800 && c < 0xE000) {\n\t\t\t\tc = (c&1023)+64; d = str.charCodeAt(i++) & 1023;\n\t\t\t\ta += 240|((c>>8)&7);              b += a; --M;\n\t\t\t\ta += 128|((c>>2)&63);             b += a; --M;\n\t\t\t\ta += 128|((d>>6)&15)|((c&3)<<4);  b += a; --M;\n\t\t\t\ta += 128|(d&63);\n\t\t\t} else {\n\t\t\t\ta += 224|((c>>12)&15);            b += a; --M;\n\t\t\t\ta += 128|((c>>6)&63);             b += a; --M;\n\t\t\t\ta += 128|(c&63);\n\t\t\t}\n\t\t\tb += a; --M;\n\t\t}\n\t\ta = (15*(a>>>16)+(a&65535));\n\t\tb = (15*(b>>>16)+(b&65535));\n\t}\n\treturn ((b%65521) << 16) | (a%65521);\n}\n// $FlowIgnore\nADLER32.bstr = adler32_bstr;\n// $FlowIgnore\nADLER32.buf = adler32_buf;\n// $FlowIgnore\nADLER32.str = adler32_str;\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AAGA,QAAI;AACJ,KAAC,SAAU,SAAS;AAGnB,UAAG,OAAO,wBAAwB,aAAa;AAC9C,YAAG,aAAa,OAAO,SAAS;AAC/B,kBAAQ,OAAO;AAAA,QAChB,WAAW,eAAe,OAAO,UAAU,OAAO,KAAK;AACtD,iBAAO,WAAY;AAClB,gBAAIA,UAAS,CAAC;AACd,oBAAQA,OAAM;AACd,mBAAOA;AAAA,UACR,CAAC;AAAA,QACF,OAAO;AACN,kBAAQ,UAAU,CAAC,CAAC;AAAA,QACrB;AAAA,MACD,OAAO;AACN,gBAAQ,UAAU,CAAC,CAAC;AAAA,MACrB;AAAA,IAGD,GAAE,SAASC,UAAS;AACpB,MAAAA,SAAQ,UAAU;AAClB,eAAS,aAAa,MAAM,MAAM;AACjC,YAAI,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI;AACvC,YAAG,OAAO,SAAS,UAAU;AAAE,cAAI,OAAO;AAAQ,cAAI,SAAS;AAAA,QAAI;AACnE,iBAAQ,IAAI,GAAG,IAAI,KAAI;AACtB,cAAI,KAAK,IAAI,IAAE,GAAG,IAAI,IAAE;AACxB,iBAAK,IAAE,GAAE,KAAK;AACb,iBAAK,KAAK,WAAW,CAAC,IAAE;AACxB,iBAAK;AAAA,UACN;AACA,cAAK,MAAI,MAAI,OAAK,IAAE;AACpB,cAAK,MAAI,MAAI,OAAK,IAAE;AAAA,QACrB;AACA,eAAS,IAAE,SAAU,KAAO,IAAE;AAAA,MAC/B;AAEA,eAAS,YAAY,KAAK,MAAM;AAC/B,YAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI;AACtC,YAAG,OAAO,SAAS,UAAU;AAAE,cAAI,OAAO;AAAQ,cAAK,SAAS,KAAM;AAAA,QAAQ;AAC9E,iBAAQ,IAAI,GAAG,IAAI,KAAI;AACtB,cAAI,KAAK,IAAI,IAAE,GAAG,IAAI,IAAE;AACxB,iBAAK,IAAE,GAAE,KAAK;AACb,iBAAK,IAAI,CAAC,IAAE;AACZ,iBAAK;AAAA,UACN;AACA,cAAK,MAAI,MAAI,OAAK,IAAE;AACpB,cAAK,MAAI,MAAI,OAAK,IAAE;AAAA,QACrB;AACA,eAAS,IAAE,SAAU,KAAO,IAAE;AAAA,MAC/B;AAEA,eAAS,YAAY,KAAK,MAAM;AAC/B,YAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,IAAI,GAAG,IAAI;AACpD,YAAG,OAAO,SAAS,UAAU;AAAE,cAAI,OAAO;AAAQ,cAAI,SAAS;AAAA,QAAI;AACnE,iBAAQ,IAAI,GAAG,IAAI,KAAI;AACtB,cAAI,KAAK,IAAI,IAAE,GAAG,IAAI;AACtB,iBAAM,IAAE,GAAG;AACV,gBAAI,IAAI,WAAW,GAAG;AACtB,gBAAG,IAAI,KAAM;AAAE,mBAAK;AAAA,YAAG,WACf,IAAI,MAAO;AAClB,mBAAK,MAAM,KAAG,IAAG;AAAiB,mBAAK;AAAG,gBAAE;AAC5C,mBAAK,MAAK,IAAE;AAAA,YACb,WAAU,KAAK,SAAU,IAAI,OAAQ;AACpC,mBAAK,IAAE,QAAM;AAAI,kBAAI,IAAI,WAAW,GAAG,IAAI;AAC3C,mBAAK,MAAM,KAAG,IAAG;AAAiB,mBAAK;AAAG,gBAAE;AAC5C,mBAAK,MAAM,KAAG,IAAG;AAAiB,mBAAK;AAAG,gBAAE;AAC5C,mBAAK,MAAM,KAAG,IAAG,MAAM,IAAE,MAAI;AAAK,mBAAK;AAAG,gBAAE;AAC5C,mBAAK,MAAK,IAAE;AAAA,YACb,OAAO;AACN,mBAAK,MAAM,KAAG,KAAI;AAAgB,mBAAK;AAAG,gBAAE;AAC5C,mBAAK,MAAM,KAAG,IAAG;AAAiB,mBAAK;AAAG,gBAAE;AAC5C,mBAAK,MAAK,IAAE;AAAA,YACb;AACA,iBAAK;AAAG,cAAE;AAAA,UACX;AACA,cAAK,MAAI,MAAI,OAAK,IAAE;AACpB,cAAK,MAAI,MAAI,OAAK,IAAE;AAAA,QACrB;AACA,eAAS,IAAE,SAAU,KAAO,IAAE;AAAA,MAC/B;AAEA,MAAAA,SAAQ,OAAO;AAEf,MAAAA,SAAQ,MAAM;AAEd,MAAAA,SAAQ,MAAM;AAAA,IACd,CAAC;AAAA;AAAA;", "names": ["module", "ADLER32"]}